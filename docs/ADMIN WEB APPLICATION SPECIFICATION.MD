# MEENA SOCIAL NETWORK - ADMIN WEB APPLICATION SPECIFICATION

## VERSION 1.0

**Document Control**  
- **Prepared For**: MEENA Development Team  
- **Prepared By**: Product Design Team  
- **Date**: October 26, 2023  
- **Status**: Approved for Development  

---

## 1. INTRODUCTION

This document specifies the complete structure for the MEENA Social Network Admin Web Application, detailing all administrative screens, navigation flows, and component specifications. The admin interface serves as the central control panel for platform moderators, administrators, and support staff to manage users, content, groups, channels, reports, verifications, and system policies.

This specification serves as the definitive reference for UI/UX design, frontend development, and quality assurance testing of the MEENA admin application.

---

## 2. ADMIN APPLICATION OVERVIEW

The MEENA Admin Web Application is a secure, role-based interface designed to manage all aspects of the MEENA social network platform. It provides comprehensive tools for content moderation, user management, verification processing, payment tracking, and policy enforcement.

### Key Features Implemented
- Role-based access control with tiered permissions
- Comprehensive user and content moderation tools
- Automated content detection with manual review workflows
- Verification processing system for profile verification
- Payment and subscription management
- Policy management and compliance tools
- Detailed analytics and reporting capabilities
- Secure audit logging for all administrative actions

---

## 3. ADMIN AUTHENTICATION & ACCESS CONTROL

### 3.1 Admin Login Screen
**Components:**
- Email and password input fields
- Two-Factor Authentication (2FA) toggle
- Language selector
- "Remember me" option
- "Forgot password?" link

**Navigation:**
- Successful login → Admin Dashboard
- "Forgot password?" → Password Recovery Screen

### 3.2 Password Recovery Screen
**Components:**
- Email input field
- Security questions verification
- OTP verification via email/SMS
- "Reset Password" button

**Navigation:**
- Successful verification → New Password Screen
- Failed verification → Error message with retry option

### 3.3 New Password Screen
**Components:**
- New password input field with strength indicator
- Password confirmation field
- Security requirements checklist
- "Set New Password" button

**Navigation:**
- Successful password change → Admin Login Screen

### 3.4 Role Selection Screen
**Components:**
- Organization structure visualization
- Role selection interface
- Permission summary
- "Continue" button

**Navigation:**
- Role selection → Admin Dashboard (with appropriate permissions)
- Insufficient permissions → Access Denied Screen

---

## 4. ADMIN DASHBOARD & MAIN NAVIGATION

### 4.1 Admin Dashboard Home
**Components:**
- Platform metrics (active users, registrations, groups, channels)
- Alert center with priority indicators
- Pending reports and verification requests
- Revenue summary (Gold memberships, verifications, ads)
- Server health and uptime monitoring
- Quick action buttons

**Navigation:**
- User Management → User Directory Screen
- Content Moderation → Content Moderation Dashboard
- Group & Channel Management → Group Management Dashboard
- Payment Management → Payment Dashboard
- Policy Management → Policy Dashboard
- System Settings → System Configuration Screen
- Analytics & Reporting → Analytics Dashboard
- Support & Helpdesk → Support Dashboard
- Admin Profile → Admin Profile Screen
- Alert Center → Alert Center Screen

### 4.2 Alert Center Screen
**Components:**
- Priority-based alert system
- Filter options (severity, category, time)
- Alert details preview
- Quick action buttons

**Navigation:**
- Alert selection → Specific alert details
- Content Moderation → Content Moderation Screen
- User Management → User Management Screen
- Group & Channel Management → Group Management Screen

---

## 5. USER MANAGEMENT SYSTEM

### 5.1 User Directory Screen
**Components:**
- Search bar (ID, phone, email, PID)
- Filter options:
  - Status (Active, Banned, Deleted, Pending Recovery)
  - Gold Membership (Yes/No)
  - Verification Status (Private, Public, Both, None)
  - Country, Device Type, Registration Date
- User table with key information
- Bulk action options

**Navigation:**
- User selection → User Detail Screen
- Verification requests → Verification Requests Screen
- Banned users → Banned Users Screen
- Gold membership → Gold Membership Screen
- Account recovery → Account Recovery Screen

### 5.2 User Detail Screen
**Components:**
- Basic information tab:
  - Profile photo, display name, bio
  - Contact information (phone, email)
  - ID, PID, join date
  - Device history (last login device & location)
- Account status tab:
  - Gold membership status
  - Verification status
  - 2FA enabled indicator
  - Remote wipe requests
- Groups & channels tab:
  - Groups/channels created
  - Groups/channels joined
  - Creation timestamps
  - Secret groups flagged indicator
- Content history tab:
  - Recent posts/stories
  - Reported messages
  - Media uploads
  - Searchable logs
- Activity log tab:
  - Login history
  - Group creation
  - Report history
  - Moderation actions
- Suspension/ban panel:
  - Reason dropdown (Racism, Pornography, Threats, etc.)
  - Duration options (Temporary: 7/30/90 days or Permanent)
  - Comment field
  - "Ban User" and "Warn & Notify" buttons

**Navigation:**
- Back → User Directory Screen
- Ban action → Confirmation modal → Action logged

### 5.3 Verification Requests Screen
**Components:**
- Filter options:
  - Type (Private Profile, Public Page, Both)
  - Status (Pending, Approved, Rejected, Payment Verified)
  - Date Submitted
- Request table:
  - User (ID, PID)
  - Submission Date
  - Document Type (Passport, ID Card)
  - Payment Status (€14.49, €24.49, €40.49)
  - Status Badge

**Navigation:**
- Request selection → Verification Detail Screen

### 5.4 Verification Detail Screen
**Components:**
- Document preview (front/back)
- AI verification results:
  - "Document Valid" indicator
  - "Photo Matches Profile" indicator
- Manual review tools:
  - Zoom and rotate controls
  - Profile photo comparison
  - Flag options (Fake ID, Mismatch, Underage)
- Action buttons:
  - Approve (triggers verification email/SMS)
  - Reject (with reason selection)
  - Request More Information

**Navigation:**
- Back → Verification Requests Screen
- Approve → Verification Confirmation Screen
- Reject → Rejection Confirmation Screen

### 5.5 Banned Users Screen
**Components:**
- Banned user list
- Ban duration and reason
- Filter options (temporary/permanent, reason)
- Appeal status indicators

**Navigation:**
- User selection → Ban Detail Screen
- Appeal review → User Appeal Screen
- Unban request → Unban Request Screen

### 5.6 Gold Membership Screen
**Components:**
- Gold membership applications list
- Payment verification status
- Filter options (status, date)
- Application details preview

**Navigation:**
- Application selection → Membership Application Detail Screen
- Payment verification → Payment Verification Screen
- Approval → Membership Approval Screen
- Rejection → Membership Rejection Screen

### 5.7 Account Recovery Screen
**Components:**
- Account recovery requests list
- Secret phrase verification status
- Filter options (status, date)
- Recovery details preview

**Navigation:**
- Request selection → Recovery Request Detail Screen
- Secret phrase verification → Secret Phrase Verification Screen
- Account restoration → Account Restoration Screen
- Recovery denial → Recovery Denial Screen

---

## 6. CONTENT MODERATION SYSTEM

### 6.1 Content Moderation Dashboard
**Components:**
- Content queue overview
- Priority indicators
- Filter options (category, severity, time)
- Statistics on processed content

**Navigation:**
- Reported content → Reported Content Screen
- Automatic detection alerts → Automatic Detection Alerts Screen
- Manual review queue → Manual Review Queue Screen
- Policy violation statistics → Policy Violation Statistics Screen

### 6.2 Reported Content Screen
**Components:**
- Filtered reported content list
- Category selection (Racism, Sexual Content, Threats, etc.)
- Reporting user information
- Reported entity details
- AI confidence score

**Navigation:**
- Content selection → Content Detail Screen
- Reporting user → Reporting User Screen
- Reported user → Reported User Screen
- Category selection → Violation Category Selection Screen

### 6.3 Content Detail Screen
**Components:**
- Full content context
- Media preview (images, videos, text)
- Reporting details and history
- AI analysis summary
- Context information (group/channel, timestamp)

**Navigation:**
- Content action → Content Action Screen
- User details → User Detail Screen
- Group/channel details → Group/Channel Detail Screen
- Evidence collection → Evidence Collection Screen

### 6.4 Automatic Detection Alerts Screen
**Components:**
- AI-detected content list
- Confidence levels indicator
- Content type classification
- Filter options (type, confidence level)
- False positive reporting option

**Navigation:**
- Alert selection → Detection Detail Screen
- False positive → False Positive Reporting Screen
- Detection tuning → Detection Tuning Screen
- Content action → Content Action Screen

### 6.5 Manual Review Queue Screen
**Components:**
- Content requiring human review
- Time-sensitive indicators
- Priority ranking
- Filter options (category, urgency)
- Escalation options

**Navigation:**
- Content selection → Content Detail Screen
- Review decision → Review Decision Screen
- Escalation request → Escalation Request Screen
- Policy reference → Policy Reference Screen

### 6.6 Content Action Screen
**Components:**
- Action selection options:
  - Warning
  - Content removal
  - Temporary ban (duration selection)
  - Permanent ban
- Action confirmation
- User notification options
- Appeal process information

**Navigation:**
- Action confirmation → Action Confirmation Screen
- User notification → User Notification Screen
- Appeal process → Appeal Process Screen
- Content details → Content Detail Screen

### 6.7 Policy Reference Screen
**Components:**
- Searchable policy database
- Policy examples
- Violation categories
- Moderation guidelines
- Related policies

**Navigation:**
- Policy selection → Specific policy details
- Violation examples → Policy Violation Examples Screen
- Content details → Content Detail Screen
- Moderation guidelines → Moderation Guidelines Screen

---

## 7. GROUP & CHANNEL MANAGEMENT

### 7.1 Group Management Dashboard
**Components:**
- Group statistics overview
- Creation requests queue
- Violation reports
- Secret group monitoring indicators
- Filter options (type, status, region)

**Navigation:**
- Group directory → Group Directory Screen
- Creation requests → Group Creation Requests Screen
- Banned groups → Banned Groups Screen
- Secret groups monitoring → Secret Groups Monitoring Screen

### 7.2 Group Directory Screen
**Components:**
- Searchable group database
- Filter options (public/private/secret, status, member count)
- Group listing with key information
- AI risk score indicators

**Navigation:**
- Group selection → Group Detail Screen
- Verification requests → Group Verification Screen
- Violation history → Group Violation History Screen
- Member management → Group Member Management Screen

### 7.3 Group Detail Screen
**Components:**
- Group information:
  - Name, photo, description
  - Privacy type
  - Invite link
  - Creation date
  - Admin list
- Member list:
  - Role (Admin, Member)
  - Join date
  - Action options (Remove, Ban)
- Message preview:
  - Recent messages (text only)
  - Search within messages
  - Highlighted keywords (AI-flagged content)
- Media & files:
  - Shared files/media list
  - Thumbnails with AI risk tags
- Moderation panel:
  - "Review Content" button
  - "Ban Group" with reason selection
  - "Warn Creator" (sends in-app notification)
  - "Request Full Logs" option

**Navigation:**
- Back → Group Directory Screen
- Verification → Group Verification Screen
- Content moderation → Content Moderation Screen
- Group action → Group Action Screen

### 7.4 Group Creation Requests Screen
**Components:**
- Pending group creation requests
- Payment verification status
- Filter options (type, status, date)
- Request details preview

**Navigation:**
- Request selection → Group Request Detail Screen
- Payment verification → Payment Verification Screen
- Group approval → Group Approval Screen
- Group rejection → Group Rejection Screen

### 7.5 Group Request Detail Screen
**Components:**
- Group type information
- Description and purpose
- Creator information
- Payment status
- Terms acceptance verification

**Navigation:**
- Payment verification → Payment Verification Screen
- Group approval → Group Approval Screen
- Group rejection → Group Rejection Screen
- User details → User Detail Screen

### 7.6 Secret Groups Monitoring Screen
**Components:**
- Secret group oversight list
- Activity monitoring indicators
- Filter options (activity level, risk)
- Priority ranking

**Navigation:**
- Group selection → Secret Group Detail Screen
- Content review → Content Review Screen
- Violation reporting → Violation Reporting Screen
- Group action → Group Action Screen

### 7.7 Banned Groups Screen
**Components:**
- Banned group list
- Ban reason and duration
- Filter options (temporary/permanent, reason)
- Appeal status indicators

**Navigation:**
- Group selection → Ban Detail Screen
- Appeal review → Appeal Review Screen
- Unban request → Unban Request Screen
- Group details → Group Detail Screen

### 7.8 Channel Management Dashboard
**Components:**
- Channel statistics overview
- Creation requests queue
- Violation reports
- Secret channel monitoring indicators
- Filter options (type, status, region)

**Navigation:**
- Channel directory → Channel Directory Screen
- Creation requests → Channel Creation Requests Screen
- Banned channels → Banned Channels Screen
- Secret channels monitoring → Secret Channels Monitoring Screen

### 7.9 Channel Directory Screen
**Components:**
- Searchable channel database
- Filter options (public/private/secret, status, subscriber count)
- Channel listing with key information
- Monetization status indicators
- Linked group information

**Navigation:**
- Channel selection → Channel Detail Screen
- Verification requests → Channel Verification Screen
- Violation history → Channel Violation History Screen
- Subscriber management → Subscriber Management Screen

### 7.10 Channel Detail Screen
**Components:**
- Channel information:
  - Name, photo, description
  - Privacy settings
  - Subscribe button status
  - Linked group (if applicable)
- Posts:
  - List of all posts
  - Engagement metrics (likes, comments, shares)
  - AI flag status indicators
- Comments:
  - Moderatable comments
  - Filter options (All, Flagged, Deleted)
- Moderation panel:
  - "Ban Channel" with reason
  - "Warn Creator" 
  - "Request Logs" option

**Navigation:**
- Back → Channel Directory Screen
- Verification → Channel Verification Screen
- Content moderation → Content Moderation Screen
- Channel action → Channel Action Screen

---

## 8. PAYMENT MANAGEMENT SYSTEM

### 8.1 Payment Dashboard
**Components:**
- Transaction overview
- Revenue statistics
- Filter options (type, date range)
- Quick access to payment categories

**Navigation:**
- Transaction history → Transaction History Screen
- Payment verification → Payment Verification Screen
- Refund management → Refund Management Screen
- Subscription management → Subscription Management Screen

### 8.2 Transaction History Screen
**Components:**
- Searchable transaction database
- Filter options (user, type, date, status)
- Transaction listing with key details
- Bulk action options

**Navigation:**
- Transaction selection → Transaction Detail Screen
- User payment history → User Payment History Screen
- Payment verification → Payment Verification Screen
- Refund request → Refund Request Screen

### 8.3 Transaction Detail Screen
**Components:**
- Complete transaction information:
  - Amount and currency
  - Payment method
  - Timestamp
  - User information
  - Transaction status
- Payment verification indicators
- Fraud detection indicators
- Refund options

**Navigation:**
- Payment verification → Payment Verification Screen
- Refund processing → Refund Processing Screen
- User details → User Detail Screen
- Payment gateway settings → Payment Gateway Settings Screen

### 8.4 Payment Verification Screen
**Components:**
- Payment confirmation interface
- Fraud detection indicators
- Verification status
- Action options:
  - Confirm payment
  - Flag as fraud
  - Request additional verification
- User communication options

**Navigation:**
- Verification confirmation → Verification Confirmation Screen
- Fraud investigation → Fraud Investigation Screen
- Transaction details → Transaction Detail Screen
- User details → User Detail Screen

### 8.5 Refund Management Screen
**Components:**
- Refund requests list
- Status tracking indicators
- Filter options (status, date, amount)
- Request details preview

**Navigation:**
- Request selection → Refund Request Detail Screen
- Refund processing → Refund Processing Screen
- User communication → User Communication Screen
- Transaction details → Transaction Detail Screen

### 8.6 Subscription Management Screen
**Components:**
- Gold membership tracking
- Renewal status indicators
- Filter options (status, date)
- Subscription details preview

**Navigation:**
- Subscription selection → Subscription Detail Screen
- Renewal processing → Renewal Processing Screen
- Cancellation management → Cancellation Management Screen
- User details → User Detail Screen

---

## 9. POLICY MANAGEMENT SYSTEM

### 9.1 Policy Dashboard
**Components:**
- Policy overview
- Update history timeline
- Compliance statistics
- Filter options (policy type, status)
- Quick access to policy categories

**Navigation:**
- Community rules → Community Rules Screen
- Terms of use → Terms of Use Screen
- Privacy policy → Privacy Policy Screen
- Intellectual property → Intellectual Property Policy Screen

### 9.2 Community Rules Screen
**Components:**
- Editable policy document
- Version history
- Search functionality
- Policy violation examples
- Implementation status indicators

**Navigation:**
- Rule selection → Rule Detail Screen
- Policy update → Policy Update Screen
- Violation examples → Violation Examples Screen
- Policy implementation → Policy Implementation Screen

### 9.3 Policy Update Screen
**Components:**
- Draft editor with formatting tools
- Change tracking indicators
- Version comparison
- Approval workflow status
- Publication options

**Navigation:**
- Update preview → Update Preview Screen
- Approval workflow → Approval Workflow Screen
- Policy document → Policy Document Screen
- Change log → Change Log Screen

### 9.4 Violation Examples Screen
**Components:**
- Categorized violation examples
- Media type filtering
- Severity indicators
- Resolution examples
- Related policy references

**Navigation:**
- Example selection → Example Detail Screen
- Policy reference → Policy Reference Screen
- Moderation guidelines → Moderation Guidelines Screen
- Content moderation → Content Moderation Screen

### 9.5 Policy Implementation Screen
**Components:**
- Enforcement settings
- Automated action configuration
- Threshold settings
- Implementation status
- Monitoring indicators

**Navigation:**
- Implementation detail → Implementation Detail Screen
- Policy update → Policy Update Screen
- Moderation settings → Moderation Settings Screen
- Policy dashboard → Policy Dashboard Screen

---

## 10. SYSTEM SETTINGS & CONFIGURATION

### 10.1 System Configuration Screen
**Components:**
- Platform-wide settings
- Feature toggles
- Maintenance mode options
- Version control indicators
- System status monitoring

**Navigation:**
- Feature management → Feature Management Screen
- Security settings → Security Settings Screen
- Notification settings → Notification Settings Screen
- API configuration → API Configuration Screen

### 10.2 Feature Management Screen
**Components:**
- Feature enable/disable controls
- Version control options
- Rollout strategy configuration
- Feature documentation
- Usage statistics

**Navigation:**
- Feature selection → Feature Detail Screen
- Rollout strategy → Rollout Strategy Screen
- Documentation → Feature Documentation Screen
- System configuration → System Configuration Screen

### 10.3 Security Settings Screen
**Components:**
- Security protocols configuration
- Access control settings
- Encryption options
- Authentication methods
- Session management

**Navigation:**
- Encryption settings → Encryption Settings Screen
- Authentication settings → Authentication Settings Screen
- Audit log → Audit Log Screen
- Security policy → Security Policy Screen

### 10.4 Notification Settings Screen
**Components:**
- Notification templates
- Delivery method configuration
- Schedule options
- User preference management
- Notification history

**Navigation:**
- Template editor → Template Editor Screen
- Delivery methods → Delivery Method Settings Screen
- Notification history → Notification History Screen
- System configuration → System Configuration Screen

### 10.5 Role Management Screen
**Components:**
- Role definitions
- Permission assignments
- Role hierarchy visualization
- User-role assignment interface
- Access control matrix

**Navigation:**
- Role selection → Role Detail Screen
- Permission assignment → Permission Assignment Screen
- User role assignment → User Role Assignment Screen
- Access control matrix → Access Control Matrix Screen

---

## 11. ANALYTICS & REPORTING

### 11.1 Analytics Dashboard
**Components:**
- Key metrics visualization
- Customizable views
- Time period selection
- Export options
- Trend analysis

**Navigation:**
- User engagement → User Engagement Screen
- Content trends → Content Trends Screen
- Financial reports → Financial Reports Screen
- Compliance reports → Compliance Reports Screen

### 11.2 User Engagement Screen
**Components:**
- Activity metrics
- Retention analysis
- Feature usage statistics
- User segmentation options
- Engagement trends

**Navigation:**
- Engagement detail → Engagement Detail Screen
- User segmentation → User Segmentation Screen
- Feature usage → Feature Usage Screen
- Analytics dashboard → Analytics Dashboard Screen

### 11.3 Content Trends Screen
**Components:**
- Content category analysis
- Growth metrics
- Trend forecasting
- Category performance comparison
- Content moderation statistics

**Navigation:**
- Trend detail → Trend Detail Screen
- Category analysis → Category Analysis Screen
- Content moderation → Content Moderation Screen
- Analytics dashboard → Analytics Dashboard Screen

### 11.4 Financial Reports Screen
**Components:**
- Revenue breakdown
- Payment method analysis
- Subscription revenue
- Ad revenue statistics
- Financial trend analysis

**Navigation:**
- Financial detail → Financial Detail Screen
- Revenue projection → Revenue Projection Screen
- Payment method analysis → Payment Method Analysis Screen
- Analytics dashboard → Analytics Dashboard Screen

### 11.5 Compliance Reports Screen
**Components:**
- Policy adherence metrics
- Violation statistics
- Moderation effectiveness
- Legal compliance indicators
- Audit trail summary

**Navigation:**
- Compliance detail → Compliance Detail Screen
- Violation analysis → Violation Analysis Screen
- Policy effectiveness → Policy Effectiveness Screen
- Analytics dashboard → Analytics Dashboard Screen

---

## 12. SUPPORT & HELPDESK

### 12.1 Support Dashboard
**Components:**
- Ticket overview
- Priority indicators
- Agent assignment status
- Resolution time metrics
- Quick action buttons

**Navigation:**
- Ticket management → Ticket Management Screen
- Knowledge base → Knowledge Base Screen
- User inquiries → User Inquiries Screen
- Technical issues → Technical Issues Screen

### 12.2 Ticket Management Screen
**Components:**
- Searchable ticket database
- Status filters
- Priority indicators
- Assignment options
- Resolution tracking

**Navigation:**
- Ticket selection → Ticket Detail Screen
- Ticket assignment → Ticket Assignment Screen
- Resolution tracking → Resolution Tracking Screen
- User communication → User Communication Screen

### 12.3 Ticket Detail Screen
**Components:**
- Complete ticket information
- Communication history
- User details
- Issue categorization
- Resolution status

**Navigation:**
- Resolution options → Resolution Options Screen
- User details → User Detail Screen
- Knowledge base → Knowledge Base Screen
- Ticket assignment → Ticket Assignment Screen

### 12.4 Resolution Options Screen
**Components:**
- Resolution paths
- Documentation links
- Resolution templates
- Escalation options
- User communication templates

**Navigation:**
- Resolution implementation → Resolution Implementation Screen
- Knowledge base → Knowledge Base Screen
- Ticket details → Ticket Detail Screen
- User communication → User Communication Screen

### 12.5 Knowledge Base Screen
**Components:**
- Searchable help documentation
- Category organization
- Article rating system
- Usage statistics
- Creation and editing tools

**Navigation:**
- Article selection → Article Detail Screen
- Article creation → Article Creation Screen
- Article editing → Article Editing Screen
- Support dashboard → Support Dashboard Screen

### 12.6 Feature Request Screen
**Components:**
- User-submitted feature requests
- Voting system
- Implementation status
- Priority indicators
- User feedback options

**Navigation:**
- Request selection → Request Detail Screen
- Implementation planning → Implementation Planning Screen
- User feedback → User Feedback Screen
- Support dashboard → Support Dashboard Screen

---

## 13. ADMIN PROFILE & SECURITY

### 13.1 Admin Profile Screen
**Components:**
- Personal information
- Role details
- Account status
- Security indicators
- Quick action buttons

**Navigation:**
- Profile settings → Profile Settings Screen
- Security settings → Security Settings Screen
- Notification preferences → Notification Preferences Screen
- Activity log → Activity Log Screen

### 13.2 Profile Settings Screen
**Components:**
- Editable profile information
- Contact details
- Role information
- Account status indicators
- Save options

**Navigation:**
- Profile confirmation → Profile Confirmation Screen
- Admin profile → Admin Profile Screen
- Security settings → Security Settings Screen

### 13.3 Security Settings Screen
**Components:**
- 2FA configuration
- Password management
- Session management
- Login history
- Security recommendations

**Navigation:**
- 2FA setup → 2FA Setup Screen
- Password change → Password Change Screen
- Session management → Session Management Screen
- Admin profile → Admin Profile Screen

### 13.4 Activity Log Screen
**Components:**
- Admin actions history
- Audit trail
- Filter options (action type, time)
- Detailed action information
- Export options

**Navigation:**
- Log detail → Log Detail Screen
- Admin profile → Admin Profile Screen
- Security settings → Security Settings Screen

---

## 14. COMPREHENSIVE NAVIGATION MAP

```
MEENA ADMIN WEB APPLICATION - COMPLETE NAVIGATION
│
├── ADMIN AUTHENTICATION
│   ├── Admin Login Screen
│   ├── Password Recovery Screen
│   ├── New Password Screen
│   └── Role Selection Screen
│
├── ADMIN DASHBOARD
│   ├── Admin Dashboard Home
│   └── Alert Center Screen
│
├── USER MANAGEMENT
│   ├── User Directory Screen
│   │   ├── User Detail Screen
│   │   │   ├── Basic Info Tab
│   │   │   ├── Account Status Tab
│   │   │   ├── Groups & Channels Tab
│   │   │   ├── Content History Tab
│   │   │   └── Activity Log Tab
│   │   ├── Verification Requests Screen
│   │   │   ├── Verification Detail Screen
│   │   │   │   ├── Verification Approval Screen
│   │   │   │   └── Verification Rejection Screen
│   │   ├── Banned Users Screen
│   │   │   ├── Ban Detail Screen
│   │   │   └── Appeal Review Screen
│   │   └── Gold Membership Screen
│   │       ├── Membership Application Detail Screen
│   │       ├── Payment Verification Screen
│   │       ├── Membership Approval Screen
│   │       └── Membership Rejection Screen
│   └── Account Recovery Screen
│       ├── Recovery Request Detail Screen
│       ├── Secret Phrase Verification Screen
│       ├── Account Restoration Screen
│       └── Recovery Denial Screen
│
├── CONTENT MODERATION
│   ├── Content Moderation Dashboard
│   │   ├── Reported Content Screen
│   │   │   ├── Content Detail Screen
│   │   │   │   └── Content Action Screen
│   │   │   │       ├── Action Confirmation Screen
│   │   │   │       └── User Notification Screen
│   │   │   └── Reporting User Screen
│   │   ├── Automatic Detection Alerts Screen
│   │   │   └── Detection Detail Screen
│   │   └── Manual Review Queue Screen
│   │       └── Policy Reference Screen
│   └── Policy Reference Screen
│       ├── Policy Violation Examples Screen
│       └── Moderation Guidelines Screen
│
├── GROUP & CHANNEL MANAGEMENT
│   ├── Group Management Dashboard
│   │   ├── Group Directory Screen
│   │   │   └── Group Detail Screen
│   │   │       ├── Group Verification Screen
│   │   │       ├── Content Moderation Screen
│   │   │       └── Group Action Screen
│   │   ├── Group Creation Requests Screen
│   │   │   └── Group Request Detail Screen
│   │   └── Secret Groups Monitoring Screen
│   │       └── Secret Group Detail Screen
│   └── Channel Management Dashboard
│       ├── Channel Directory Screen
│       │   └── Channel Detail Screen
│       │       ├── Channel Verification Screen
│       │       ├── Content Moderation Screen
│       │       └── Channel Action Screen
│       ├── Channel Creation Requests Screen
│       │   └── Channel Request Detail Screen
│       └── Secret Channels Monitoring Screen
│           └── Secret Channel Detail Screen
│
├── PAYMENT MANAGEMENT
│   ├── Payment Dashboard
│   │   ├── Transaction History Screen
│   │   │   └── Transaction Detail Screen
│   │   │       ├── Payment Verification Screen
│   │   │       └── Refund Request Screen
│   │   ├── Payment Verification Screen
│   │   ├── Refund Management Screen
│   │   └── Subscription Management Screen
│   └── Subscription Management Screen
│       ├── Subscription Detail Screen
│       └── Renewal Processing Screen
│
├── POLICY MANAGEMENT
│   ├── Policy Dashboard
│   │   ├── Community Rules Screen
│   │   │   ├── Rule Detail Screen
│   │   │   ├── Policy Update Screen
│   │   │   │   └── Update Preview Screen
│   │   │   └── Violation Examples Screen
│   │   ├── Terms of Use Screen
│   │   ├── Privacy Policy Screen
│   │   └── Intellectual Property Policy Screen
│   └── Policy Implementation Screen
│       ├── Implementation Detail Screen
│       └── Moderation Settings Screen
│
├── SYSTEM SETTINGS
│   ├── System Configuration Screen
│   │   ├── Feature Management Screen
│   │   │   └── Feature Detail Screen
│   │   ├── Security Settings Screen
│   │   │   ├── Encryption Settings Screen
│   │   │   └── Authentication Settings Screen
│   │   ├── Notification Settings Screen
│   │   │   └── Template Editor Screen
│   │   └── Role Management Screen
│   │       ├── Role Detail Screen
│   │       └── Permission Assignment Screen
│   └── Access Control Matrix Screen
│       └── Role Management Screen
│
├── ANALYTICS & REPORTING
│   ├── Analytics Dashboard
│   │   ├── User Engagement Screen
│   │   │   └── Engagement Detail Screen
│   │   ├── Content Trends Screen
│   │   │   └── Trend Detail Screen
│   │   ├── Financial Reports Screen
│   │   │   └── Financial Detail Screen
│   │   └── Compliance Reports Screen
│   │       └── Compliance Detail Screen
│   └── Report Export Screen
│       ├── PDF Export Screen
│       └── CSV Export Screen
│
├── SUPPORT & HELPDESK
│   ├── Support Dashboard
│   │   ├── Ticket Management Screen
│   │   │   └── Ticket Detail Screen
│   │   │       ├── Resolution Options Screen
│   │   │       └── Ticket Assignment Screen
│   │   ├── Knowledge Base Screen
│   │   │   ├── Article Detail Screen
│   │   │   └── Article Creation Screen
│   │   └── Feature Request Screen
│   │       └── Request Detail Screen
│   └── User Communication Screen
│       ├── Message Template Screen
│       └── Ticket Detail Screen
│
└── ADMIN PROFILE
    ├── Admin Profile Screen
    │   ├── Profile Settings Screen
    │   ├── Security Settings Screen
    │   │   ├── 2FA Setup Screen
    │   │   └── Password Change Screen
    │   ├── Notification Preferences Screen
    │   └── Activity Log Screen
    └── Session Management Screen
        └── Active Sessions Screen
```

---

## 15. IMPLEMENTATION REQUIREMENTS

### 15.1 Role-Based Access Control
- **Super Admin**: Full access, delete admins, change policies
- **Senior Moderator**: Ban users/groups, approve verifications, view all data
- **Junior Moderator**: Review reports, warn users, flag content
- **Support Agent**: View user profiles, reset 2FA, respond to help tickets
- **Finance Officer**: View payments, export reports, manage ads
- **Compliance Officer**: Handle legal requests, export data, manage takedowns

### 15.2 Content Moderation Requirements
- **Two-tier moderation system**:
  - Automatic for public/private groups/channels
  - Manual for secret groups/channels
- **Specific violation categories**:
  - Racism
  - Sexual Content
  - Threats
  - Violence and Prejudices
  - Hate Speech
  - Terrorism and Violent Extremism
  - Harassment and Intimidation
- **Differentiated enforcement**:
  - Automatic bans for public/private groups violating policies
  - Manual review for secret groups with potentially illegal content

### 15.3 Group & Channel Management Requirements
- **Clear distinction between types**:
  - Public (unlimited members)
  - Private (link access)
  - Secret (paid, limited freedom)
- **Payment verification system for**:
  - Additional public/private groups beyond 2 (€2.49 each)
  - Secret groups (€6.49)
  - Additional public/private channels beyond 2 (€4.49 each)
  - Secret channels (€12.49)
- **Subgroup management interface** (free subgroups within main groups)

### 15.4 Verification System Requirements
- **Document verification interface** supporting multiple ID types
- **Verification process** including payment verification for paid verifications
- **Clear fee display**:
  - €14.49 for private profile verification
  - €24.49 for public page verification
  - €40.49 for both verifications
- **Moderator approval workflow** before payment collection

### 15.5 Gold Membership Management
- **€80 lifetime membership tracking**
- **Benefits verification**:
  - Unlimited groups/channels
  - Unlimited calls
  - Remote profile deletion
  - Unsubscribe from default accounts
- **Subscription status monitoring**

### 15.6 Account Recovery System
- **Secret phrase verification interface** (9-word African language phrases)
- **60-day recovery window enforcement**
- **PIN verification system** for account recovery

### 15.7 Security Requirements
- **Role-based access control** with different permission levels
- **Audit trail** for all admin actions
- **Secure session management**
- **Two-factor authentication enforcement**

### 15.8 Reporting & Analytics
- **Comprehensive reporting** on content violations
- **Financial reporting** for all payment types
- **User engagement metrics**
- **Policy effectiveness analysis**

---

## 16. NEXT STEPS FOR DEVELOPMENT

1. Create detailed wireframes for each screen based on this specification
2. Develop a component library for consistent UI implementation across the admin interface
3. Prioritize screens for Version 01 implementation based on critical functionality
4. Implement role-based access control with the defined permission levels
5. Build the content moderation system with the required two-tier approach
6. Configure the payment verification system with the correct pricing structure
7. Implement the secret phrase recovery system using the required African languages
8. Create the automated detection system for prohibited content
9. Build the analytics and reporting capabilities with the specified metrics
10. Configure the notification system for all administrative actions

---

*This document is the property of MEENA and contains proprietary information. Unauthorized distribution is prohibited.*