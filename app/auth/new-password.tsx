import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';

export default function NewPassword() {
  const handlePasswordSet = () => {
    // Simulate successful password change
    router.push('/auth/login');
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Set New Password</Text>
        <Text style={styles.subtitle}>Create a Secure Admin Password</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen allows administrators to set a new secure password after successful 
          verification. It enforces strong password policies to maintain system security.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• New password input field with strength indicator</Text>
          <Text style={styles.component}>• Password confirmation field</Text>
          <Text style={styles.component}>• Security requirements checklist</Text>
          <Text style={styles.component}>• "Set New Password" button</Text>
        </View>

        <Text style={styles.sectionTitle}>Password Requirements:</Text>
        <View style={styles.requirementsList}>
          <Text style={styles.requirement}>✓ Minimum 12 characters length</Text>
          <Text style={styles.requirement}>✓ At least one uppercase letter</Text>
          <Text style={styles.requirement}>✓ At least one lowercase letter</Text>
          <Text style={styles.requirement}>✓ At least one number</Text>
          <Text style={styles.requirement}>✓ At least one special character</Text>
          <Text style={styles.requirement}>✓ No common dictionary words</Text>
          <Text style={styles.requirement}>✓ Different from last 5 passwords</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationSection}>
          <TouchableOpacity style={styles.primaryButton} onPress={handlePasswordSet}>
            <Text style={styles.primaryButtonText}>Set New Password (Demo)</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Successful password change → Admin Login Screen
          {'\n'}• Password validation failure → Error message with requirements
        </Text>

        <Text style={styles.sectionTitle}>Security Features:</Text>
        <View style={styles.securityList}>
          <Text style={styles.security}>• Real-time password strength validation</Text>
          <Text style={styles.security}>• Password history checking</Text>
          <Text style={styles.security}>• Secure password hashing (bcrypt)</Text>
          <Text style={styles.security}>• Password expiration policy (90 days)</Text>
          <Text style={styles.security}>• Audit logging of password changes</Text>
          <Text style={styles.security}>• Email notification of password change</Text>
        </View>

        <Text style={styles.sectionTitle}>Password Strength Indicator:</Text>
        <View style={styles.strengthIndicator}>
          <Text style={styles.strengthText}>Password Strength: </Text>
          <View style={styles.strengthBar}>
            <View style={[styles.strengthSegment, styles.weak]} />
            <View style={[styles.strengthSegment, styles.medium]} />
            <View style={[styles.strengthSegment, styles.strong]} />
            <View style={[styles.strengthSegment, styles.veryStrong]} />
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#27ae60',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  requirementsList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  requirement: {
    fontSize: 14,
    color: '#27ae60',
    marginBottom: 8,
    lineHeight: 20,
  },
  navigationSection: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  primaryButton: {
    backgroundColor: '#27ae60',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  securityList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  security: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  strengthIndicator: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  strengthText: {
    fontSize: 14,
    color: '#555',
    marginRight: 12,
  },
  strengthBar: {
    flexDirection: 'row',
    flex: 1,
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  strengthSegment: {
    flex: 1,
    marginRight: 2,
  },
  weak: {
    backgroundColor: '#e74c3c',
  },
  medium: {
    backgroundColor: '#f39c12',
  },
  strong: {
    backgroundColor: '#f1c40f',
  },
  veryStrong: {
    backgroundColor: '#27ae60',
  },
});
