import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';

export default function RoleSelection() {
  const handleRoleSelection = (role: string) => {
    // Simulate role selection and redirect to admin dashboard
    router.push('/admin/dashboard');
  };

  const roles = [
    {
      id: 'super-admin',
      name: 'Super Admin',
      description: 'Full access, delete admins, change policies',
      color: '#8e44ad',
      permissions: ['All system permissions', 'User management', 'Policy changes', 'Admin management']
    },
    {
      id: 'senior-moderator',
      name: 'Senior Moderator',
      description: 'Ban users/groups, approve verifications, view all data',
      color: '#2980b9',
      permissions: ['Content moderation', 'User banning', 'Verification approval', 'Data access']
    },
    {
      id: 'junior-moderator',
      name: 'Junior Moderator',
      description: 'Review reports, warn users, flag content',
      color: '#27ae60',
      permissions: ['Report review', 'User warnings', 'Content flagging', 'Basic moderation']
    },
    {
      id: 'support-agent',
      name: 'Support Agent',
      description: 'View user profiles, reset 2FA, respond to help tickets',
      color: '#f39c12',
      permissions: ['User support', '2FA reset', 'Ticket management', 'Profile viewing']
    },
    {
      id: 'finance-officer',
      name: 'Finance Officer',
      description: 'View payments, export reports, manage ads',
      color: '#e67e22',
      permissions: ['Payment management', 'Financial reports', 'Ad management', 'Revenue tracking']
    },
    {
      id: 'compliance-officer',
      name: 'Compliance Officer',
      description: 'Handle legal requests, export data, manage takedowns',
      color: '#c0392b',
      permissions: ['Legal compliance', 'Data export', 'Content takedowns', 'Regulatory reporting']
    }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Role Selection</Text>
        <Text style={styles.subtitle}>Choose Your Administrative Role</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen allows authenticated administrators to select their role based on their 
          assigned permissions. Each role has specific access levels and capabilities within 
          the MEENA admin system.
        </Text>

        <Text style={styles.sectionTitle}>Available Roles:</Text>
        
        {roles.map((role) => (
          <TouchableOpacity 
            key={role.id}
            style={[styles.roleCard, { borderLeftColor: role.color }]}
            onPress={() => handleRoleSelection(role.id)}
          >
            <View style={styles.roleHeader}>
              <Text style={[styles.roleName, { color: role.color }]}>{role.name}</Text>
              <View style={[styles.roleIndicator, { backgroundColor: role.color }]} />
            </View>
            <Text style={styles.roleDescription}>{role.description}</Text>
            <View style={styles.permissionsList}>
              {role.permissions.map((permission, index) => (
                <Text key={index} style={styles.permission}>• {permission}</Text>
              ))}
            </View>
          </TouchableOpacity>
        ))}

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Role selection → Admin Dashboard (with appropriate permissions)
          {'\n'}• Insufficient permissions → Access Denied Screen
          {'\n'}• Role-based menu filtering and feature access
        </Text>

        <Text style={styles.sectionTitle}>Organization Structure:</Text>
        <View style={styles.hierarchyContainer}>
          <Text style={styles.hierarchyTitle}>Permission Hierarchy:</Text>
          <Text style={styles.hierarchyLevel}>1. Super Admin (Full Control)</Text>
          <Text style={styles.hierarchyLevel}>2. Senior Moderator (Content & Users)</Text>
          <Text style={styles.hierarchyLevel}>3. Junior Moderator (Basic Moderation)</Text>
          <Text style={styles.hierarchyLevel}>4. Support Agent (User Assistance)</Text>
          <Text style={styles.hierarchyLevel}>5. Finance Officer (Financial Data)</Text>
          <Text style={styles.hierarchyLevel}>6. Compliance Officer (Legal & Regulatory)</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#9b59b6',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  roleCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  roleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  roleName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  roleIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  roleDescription: {
    fontSize: 14,
    color: '#555',
    marginBottom: 12,
    lineHeight: 20,
  },
  permissionsList: {
    marginTop: 8,
  },
  permission: {
    fontSize: 12,
    color: '#777',
    marginBottom: 4,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  hierarchyContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  hierarchyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  hierarchyLevel: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    paddingLeft: 16,
  },
});
