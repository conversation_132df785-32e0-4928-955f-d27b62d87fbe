import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link, router } from 'expo-router';

export default function PasswordRecovery() {
  const handleVerification = () => {
    // Simulate successful verification
    router.push('/auth/new-password');
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Password Recovery</Text>
        <Text style={styles.subtitle}>Secure Account Recovery Process</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen handles the password recovery process for admin accounts. It provides multiple 
          verification methods to ensure secure account recovery while maintaining system security.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Email input field</Text>
          <Text style={styles.component}>• Security questions verification</Text>
          <Text style={styles.component}>• OTP verification via email/SMS</Text>
          <Text style={styles.component}>• "Reset Password" button</Text>
        </View>

        <Text style={styles.sectionTitle}>Recovery Process:</Text>
        <View style={styles.processList}>
          <Text style={styles.processStep}>1. Enter registered email address</Text>
          <Text style={styles.processStep}>2. Answer security questions</Text>
          <Text style={styles.processStep}>3. Verify OTP sent to email/SMS</Text>
          <Text style={styles.processStep}>4. Proceed to set new password</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationSection}>
          <TouchableOpacity style={styles.primaryButton} onPress={handleVerification}>
            <Text style={styles.primaryButtonText}>Verify & Continue (Demo)</Text>
          </TouchableOpacity>

          <Link href="/auth/login" asChild>
            <TouchableOpacity style={styles.secondaryButton}>
              <Text style={styles.secondaryButtonText}>Back to Login</Text>
            </TouchableOpacity>
          </Link>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Successful verification → New Password Screen
          {'\n'}• Failed verification → Error message with retry option
          {'\n'}• Back to Login → Admin Login Screen
        </Text>

        <Text style={styles.sectionTitle}>Security Features:</Text>
        <View style={styles.securityList}>
          <Text style={styles.security}>• Multi-factor verification process</Text>
          <Text style={styles.security}>• Time-limited OTP codes</Text>
          <Text style={styles.security}>• Account lockout after failed attempts</Text>
          <Text style={styles.security}>• Audit logging of recovery attempts</Text>
          <Text style={styles.security}>• Email notifications for security events</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e74c3c',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  processList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  processStep: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    paddingLeft: 8,
  },
  navigationSection: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  primaryButton: {
    backgroundColor: '#e74c3c',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  secondaryButton: {
    backgroundColor: '#95a5a6',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  securityList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  security: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
});
