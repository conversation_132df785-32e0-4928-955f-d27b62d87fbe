import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function AdminDashboard() {
  const dashboardSections = [
    {
      title: 'User Management',
      route: '/admin/users',
      color: '#3498db',
      description: 'Manage users, verifications, bans, and gold memberships'
    },
    {
      title: 'Content Moderation',
      route: '/admin/content',
      color: '#e74c3c',
      description: 'Review reported content, automatic detection alerts'
    },
    {
      title: 'Group & Channel Management',
      route: '/admin/groups',
      color: '#9b59b6',
      description: 'Manage groups, channels, creation requests'
    },
    {
      title: 'Payment Management',
      route: '/admin/payments',
      color: '#f39c12',
      description: 'Handle transactions, verifications, refunds'
    },
    {
      title: 'Policy Management',
      route: '/admin/policies',
      color: '#1abc9c',
      description: 'Manage community rules, terms, privacy policies'
    },
    {
      title: 'System Settings',
      route: '/admin/system',
      color: '#34495e',
      description: 'Configure system, features, security settings'
    },
    {
      title: 'Analytics & Reporting',
      route: '/admin/analytics',
      color: '#27ae60',
      description: 'View engagement, trends, financial reports'
    },
    {
      title: 'Support & Helpdesk',
      route: '/admin/support',
      color: '#e67e22',
      description: 'Manage tickets, knowledge base, user inquiries'
    }
  ];

  const metrics = [
    { label: 'Active Users', value: '1,234,567', color: '#3498db' },
    { label: 'New Registrations (24h)', value: '2,345', color: '#27ae60' },
    { label: 'Active Groups', value: '45,678', color: '#9b59b6' },
    { label: 'Active Channels', value: '12,345', color: '#e74c3c' },
    { label: 'Pending Reports', value: '89', color: '#f39c12' },
    { label: 'Gold Members', value: '5,432', color: '#f1c40f' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>MEENA Admin Dashboard</Text>
        <Text style={styles.subtitle}>Central Control Panel</Text>
        <Link href="/admin/alert-center" asChild>
          <TouchableOpacity style={styles.alertButton}>
            <Text style={styles.alertButtonText}>Alert Center (3)</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This is the main Admin Dashboard Home screen that serves as the central control panel 
          for the MEENA Social Network. It provides quick access to all administrative functions 
          and displays key platform metrics.
        </Text>

        <Text style={styles.sectionTitle}>Platform Metrics:</Text>
        <View style={styles.metricsGrid}>
          {metrics.map((metric, index) => (
            <View key={index} style={[styles.metricCard, { borderLeftColor: metric.color }]}>
              <Text style={styles.metricValue}>{metric.value}</Text>
              <Text style={styles.metricLabel}>{metric.label}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Administrative Sections:</Text>
        {dashboardSections.map((section, index) => (
          <Link key={index} href={section.route} asChild>
            <TouchableOpacity style={[styles.sectionCard, { borderLeftColor: section.color }]}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionName, { color: section.color }]}>{section.title}</Text>
                <View style={[styles.sectionIndicator, { backgroundColor: section.color }]} />
              </View>
              <Text style={styles.sectionDescription}>{section.description}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.quickActions}>
          <Link href="/admin/users/directory" asChild>
            <TouchableOpacity style={styles.quickActionButton}>
              <Text style={styles.quickActionText}>User Search</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/content/reported" asChild>
            <TouchableOpacity style={styles.quickActionButton}>
              <Text style={styles.quickActionText}>Review Reports</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/groups/directory" asChild>
            <TouchableOpacity style={styles.quickActionButton}>
              <Text style={styles.quickActionText}>Group Management</Text>
            </TouchableOpacity>
          </Link>
        </View>

        <Text style={styles.sectionTitle}>System Status:</Text>
        <View style={styles.systemStatus}>
          <Text style={styles.statusItem}>🟢 Server Health: Excellent</Text>
          <Text style={styles.statusItem}>🟢 Database: Online</Text>
          <Text style={styles.statusItem}>🟢 Content Detection: Active</Text>
          <Text style={styles.statusItem}>🟡 Backup Status: In Progress</Text>
        </View>

        <View style={styles.footer}>
          <Link href="/admin/profile" asChild>
            <TouchableOpacity style={styles.profileButton}>
              <Text style={styles.profileButtonText}>Admin Profile & Settings</Text>
            </TouchableOpacity>
          </Link>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#2c3e50',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#ecf0f1',
    marginBottom: 16,
  },
  alertButton: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  alertButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  metricCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  metricLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    marginTop: 4,
  },
  sectionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  sectionIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  quickActionButton: {
    backgroundColor: '#3498db',
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  quickActionText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  systemStatus: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusItem: {
    fontSize: 14,
    marginBottom: 8,
  },
  footer: {
    marginTop: 24,
    alignItems: 'center',
  },
  profileButton: {
    backgroundColor: '#95a5a6',
    padding: 16,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
  },
  profileButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
