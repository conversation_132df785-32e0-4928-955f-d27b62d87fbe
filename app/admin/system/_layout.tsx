import { Stack } from 'expo-router';

export default function SystemLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'System Settings',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="configuration" 
        options={{ 
          title: 'System Configuration',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="features" 
        options={{ 
          title: 'Feature Management',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="security" 
        options={{ 
          title: 'Security Settings',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="notifications" 
        options={{ 
          title: 'Notification Settings',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="roles" 
        options={{ 
          title: 'Role Management',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="encryption" 
        options={{ 
          title: 'Encryption Settings',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="authentication" 
        options={{ 
          title: 'Authentication Settings',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="audit-log" 
        options={{ 
          title: 'Audit Log',
          headerShown: false 
        }} 
      />
    </Stack>
  );
}
