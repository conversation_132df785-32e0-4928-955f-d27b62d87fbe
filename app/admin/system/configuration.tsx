import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch } from 'react-native';
import { Link } from 'expo-router';
import { useState } from 'react';

export default function SystemConfiguration() {
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [debugMode, setDebugMode] = useState(false);
  const [autoBackup, setAutoBackup] = useState(true);

  const systemSections = [
    {
      title: 'Feature Management',
      route: '/admin/system/features',
      color: '#3498db',
      description: 'Enable/disable platform features and manage rollouts',
      status: 'Active'
    },
    {
      title: 'Security Settings',
      route: '/admin/system/security',
      color: '#e74c3c',
      description: 'Configure security protocols and access controls',
      status: 'Active'
    },
    {
      title: 'Notification Settings',
      route: '/admin/system/notifications',
      color: '#f39c12',
      description: 'Manage notification templates and delivery methods',
      status: 'Active'
    },
    {
      title: 'Role Management',
      route: '/admin/system/roles',
      color: '#9b59b6',
      description: 'Define roles, permissions, and access control matrix',
      status: 'Active'
    },
    {
      title: 'Encryption Settings',
      route: '/admin/system/encryption',
      color: '#27ae60',
      description: 'Configure encryption protocols and key management',
      status: 'Active'
    },
    {
      title: 'Authentication Settings',
      route: '/admin/system/authentication',
      color: '#e67e22',
      description: 'Manage authentication methods and session controls',
      status: 'Active'
    }
  ];

  const systemMetrics = [
    { label: 'Server Uptime', value: '99.9%', color: '#27ae60' },
    { label: 'Active Sessions', value: '12,345', color: '#3498db' },
    { label: 'Database Size', value: '2.4 TB', color: '#f39c12' },
    { label: 'Daily Backups', value: '✓ Complete', color: '#27ae60' }
  ];

  const platformSettings = [
    {
      id: 'maintenance',
      title: 'Maintenance Mode',
      description: 'Enable maintenance mode to restrict user access',
      value: maintenanceMode,
      onChange: setMaintenanceMode,
      critical: true
    },
    {
      id: 'debug',
      title: 'Debug Mode',
      description: 'Enable detailed logging for troubleshooting',
      value: debugMode,
      onChange: setDebugMode,
      critical: false
    },
    {
      id: 'backup',
      title: 'Automatic Backups',
      description: 'Perform daily automated system backups',
      value: autoBackup,
      onChange: setAutoBackup,
      critical: false
    }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>System Configuration</Text>
        <Text style={styles.subtitle}>Platform-Wide Settings & Controls</Text>
        <Link href="/admin/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          The System Configuration screen provides centralized control over all 
          platform-wide settings, including feature toggles, maintenance mode, 
          version control, and system status monitoring.
        </Text>

        <Text style={styles.sectionTitle}>System Metrics:</Text>
        <View style={styles.metricsGrid}>
          {systemMetrics.map((metric, index) => (
            <View key={index} style={[styles.metricCard, { borderTopColor: metric.color }]}>
              <Text style={styles.metricValue}>{metric.value}</Text>
              <Text style={styles.metricLabel}>{metric.label}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Platform Settings:</Text>
        {platformSettings.map((setting) => (
          <View key={setting.id} style={[
            styles.settingCard,
            setting.critical && styles.criticalSetting
          ]}>
            <View style={styles.settingHeader}>
              <View style={styles.settingInfo}>
                <Text style={[
                  styles.settingTitle,
                  setting.critical && styles.criticalTitle
                ]}>
                  {setting.title}
                  {setting.critical && <Text style={styles.criticalBadge}> CRITICAL</Text>}
                </Text>
                <Text style={styles.settingDescription}>{setting.description}</Text>
              </View>
              <Switch
                value={setting.value}
                onValueChange={setting.onChange}
                trackColor={{ false: '#ddd', true: setting.critical ? '#e74c3c' : '#27ae60' }}
                thumbColor={setting.value ? '#fff' : '#f4f3f4'}
              />
            </View>
          </View>
        ))}

        <Text style={styles.sectionTitle}>Configuration Sections:</Text>
        {systemSections.map((section, index) => (
          <Link key={index} href={section.route} asChild>
            <TouchableOpacity style={[styles.sectionCard, { borderLeftColor: section.color }]}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: section.color }]}>{section.title}</Text>
                <View style={[styles.statusBadge, { backgroundColor: '#27ae60' }]}>
                  <Text style={styles.statusText}>{section.status}</Text>
                </View>
              </View>
              <Text style={styles.sectionDescription}>{section.description}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>System Status:</Text>
        <View style={styles.statusContainer}>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>🟢 Application Server:</Text>
            <Text style={styles.statusValue}>Online</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>🟢 Database:</Text>
            <Text style={styles.statusValue}>Connected</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>🟢 Redis Cache:</Text>
            <Text style={styles.statusValue}>Active</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>🟡 File Storage:</Text>
            <Text style={styles.statusValue}>85% Capacity</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>🟢 CDN:</Text>
            <Text style={styles.statusValue}>Operational</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>🟢 Monitoring:</Text>
            <Text style={styles.statusValue}>All Systems</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.quickActions}>
          <Link href="/admin/system/audit-log" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#34495e' }]}>
              <Text style={styles.actionButtonText}>View Audit Log</Text>
            </TouchableOpacity>
          </Link>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#e74c3c' }]}>
            <Text style={styles.actionButtonText}>System Restart</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#27ae60' }]}>
            <Text style={styles.actionButtonText}>Run Backup</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Feature management → Feature Management Screen → Feature toggles
          {'\n'}• Security settings → Security Settings Screen → Protocol configuration
          {'\n'}• Notification settings → Notification Settings Screen → Template editor
          {'\n'}• Role management → Role Management Screen → Permission matrix
          {'\n'}• Audit log → Audit Log Screen → System activity history
        </Text>

        <Text style={styles.sectionTitle}>System Configuration Features:</Text>
        <View style={styles.featuresCard}>
          <Text style={styles.featureTitle}>Advanced System Control:</Text>
          <Text style={styles.feature}>• Real-time system monitoring and alerts</Text>
          <Text style={styles.feature}>• Automated backup and recovery systems</Text>
          <Text style={styles.feature}>• Feature flag management and rollouts</Text>
          <Text style={styles.feature}>• Performance optimization controls</Text>
          <Text style={styles.feature}>• Security protocol configuration</Text>
          <Text style={styles.feature}>• Comprehensive audit logging</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#34495e',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  metricCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderTopWidth: 4,
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  metricLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: 4,
  },
  settingCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  criticalSetting: {
    borderLeftWidth: 4,
    borderLeftColor: '#e74c3c',
  },
  settingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  criticalTitle: {
    color: '#e74c3c',
  },
  criticalBadge: {
    fontSize: 10,
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  settingDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  sectionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  sectionDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  statusContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusLabel: {
    fontSize: 14,
    color: '#555',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  featuresCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
});
