import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function AnalyticsDashboard() {
  const analyticsCategories = [
    {
      title: 'User Engagement',
      route: '/admin/analytics/user-engagement',
      color: '#3498db',
      description: 'Activity metrics, retention analysis, and feature usage',
      metrics: { primary: '85.2%', secondary: 'Avg. Engagement' }
    },
    {
      title: 'Content Trends',
      route: '/admin/analytics/content-trends',
      color: '#e74c3c',
      description: 'Content category analysis and growth metrics',
      metrics: { primary: '+23%', secondary: 'Content Growth' }
    },
    {
      title: 'Financial Reports',
      route: '/admin/analytics/financial-reports',
      color: '#f39c12',
      description: 'Revenue breakdown and payment analysis',
      metrics: { primary: '€125K', secondary: 'Monthly Revenue' }
    },
    {
      title: 'Compliance Reports',
      route: '/admin/analytics/compliance-reports',
      color: '#9b59b6',
      description: 'Policy adherence and violation statistics',
      metrics: { primary: '97.8%', secondary: 'Compliance Rate' }
    }
  ];

  const keyMetrics = [
    { label: 'Daily Active Users', value: '234K', change: '+5.2%', color: '#27ae60' },
    { label: 'Monthly Revenue', value: '€125K', change: '+12.8%', color: '#f39c12' },
    { label: 'Content Created', value: '45.2K', change: '+8.1%', color: '#3498db' },
    { label: 'Moderation Actions', value: '1.2K', change: '-3.4%', color: '#e74c3c' }
  ];

  const timeRanges = ['24h', '7d', '30d', '90d', '1y'];
  const selectedRange = '30d';

  const topPerformingContent = [
    { type: 'Images', count: '18.5K', percentage: '42%' },
    { type: 'Text Posts', count: '12.3K', percentage: '28%' },
    { type: 'Videos', count: '8.7K', percentage: '20%' },
    { type: 'Links', count: '4.4K', percentage: '10%' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Analytics Dashboard</Text>
        <Text style={styles.subtitle}>Comprehensive Platform Analytics</Text>
        <Link href="/admin/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          The Analytics Dashboard provides comprehensive insights into platform 
          performance, user behavior, content trends, and financial metrics. 
          It offers customizable views and detailed reporting capabilities.
        </Text>

        <Text style={styles.sectionTitle}>Time Period Selection:</Text>
        <View style={styles.timeRangeContainer}>
          {timeRanges.map((range) => (
            <TouchableOpacity 
              key={range}
              style={[
                styles.timeRangeButton,
                range === selectedRange && styles.activeTimeRange
              ]}
            >
              <Text style={[
                styles.timeRangeText,
                range === selectedRange && styles.activeTimeRangeText
              ]}>
                {range}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Key Metrics:</Text>
        <View style={styles.metricsGrid}>
          {keyMetrics.map((metric, index) => (
            <View key={index} style={[styles.metricCard, { borderTopColor: metric.color }]}>
              <Text style={styles.metricValue}>{metric.value}</Text>
              <Text style={styles.metricLabel}>{metric.label}</Text>
              <Text style={[
                styles.metricChange,
                { color: metric.change.startsWith('+') ? '#27ae60' : '#e74c3c' }
              ]}>
                {metric.change}
              </Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Analytics Categories:</Text>
        {analyticsCategories.map((category, index) => (
          <Link key={index} href={category.route} asChild>
            <TouchableOpacity style={[styles.categoryCard, { borderLeftColor: category.color }]}>
              <View style={styles.categoryHeader}>
                <Text style={[styles.categoryTitle, { color: category.color }]}>
                  {category.title}
                </Text>
                <View style={styles.categoryMetrics}>
                  <Text style={styles.primaryMetric}>{category.metrics.primary}</Text>
                  <Text style={styles.secondaryMetric}>{category.metrics.secondary}</Text>
                </View>
              </View>
              <Text style={styles.categoryDescription}>{category.description}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Content Performance:</Text>
        <View style={styles.contentPerformanceCard}>
          <Text style={styles.performanceTitle}>Top Performing Content Types (30d):</Text>
          {topPerformingContent.map((content, index) => (
            <View key={index} style={styles.performanceItem}>
              <View style={styles.performanceInfo}>
                <Text style={styles.performanceType}>{content.type}</Text>
                <Text style={styles.performanceCount}>{content.count} posts</Text>
              </View>
              <Text style={styles.performancePercentage}>{content.percentage}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Quick Export Options:</Text>
        <View style={styles.exportActions}>
          <TouchableOpacity style={[styles.exportButton, { backgroundColor: '#27ae60' }]}>
            <Text style={styles.exportButtonText}>Export PDF</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.exportButton, { backgroundColor: '#3498db' }]}>
            <Text style={styles.exportButtonText}>Export CSV</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.exportButton, { backgroundColor: '#9b59b6' }]}>
            <Text style={styles.exportButtonText}>Schedule Report</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Analytics Features:</Text>
        <View style={styles.featuresCard}>
          <Text style={styles.featureTitle}>Advanced Analytics Capabilities:</Text>
          <Text style={styles.feature}>• Real-time data visualization and dashboards</Text>
          <Text style={styles.feature}>• Custom date range selection and filtering</Text>
          <Text style={styles.feature}>• Automated report generation and scheduling</Text>
          <Text style={styles.feature}>• Trend analysis and forecasting</Text>
          <Text style={styles.feature}>• User segmentation and cohort analysis</Text>
          <Text style={styles.feature}>• Export capabilities (PDF, CSV, Excel)</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • User engagement → User Engagement Screen → Engagement Detail
          {'\n'}• Content trends → Content Trends Screen → Trend Detail
          {'\n'}• Financial reports → Financial Reports Screen → Revenue Analysis
          {'\n'}• Compliance reports → Compliance Reports Screen → Violation Analysis
        </Text>

        <Text style={styles.sectionTitle}>Data Sources:</Text>
        <View style={styles.dataSourcesCard}>
          <Text style={styles.dataSourceTitle}>Integrated Data Sources:</Text>
          <Text style={styles.dataSource}>• User activity and engagement tracking</Text>
          <Text style={styles.dataSource}>• Content creation and interaction metrics</Text>
          <Text style={styles.dataSource}>• Payment and subscription data</Text>
          <Text style={styles.dataSource}>• Moderation and compliance actions</Text>
          <Text style={styles.dataSource}>• System performance and uptime</Text>
          <Text style={styles.dataSource}>• Third-party analytics integrations</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#27ae60',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  timeRangeContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 4,
    marginBottom: 16,
  },
  timeRangeButton: {
    flex: 1,
    padding: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeTimeRange: {
    backgroundColor: '#27ae60',
  },
  timeRangeText: {
    fontSize: 14,
    color: '#555',
  },
  activeTimeRangeText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  metricCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderTopWidth: 4,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  metricLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    marginTop: 4,
  },
  metricChange: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 4,
  },
  categoryCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  categoryMetrics: {
    alignItems: 'flex-end',
  },
  primaryMetric: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  secondaryMetric: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  categoryDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  contentPerformanceCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  performanceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  performanceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  performanceInfo: {
    flex: 1,
  },
  performanceType: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  performanceCount: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  performancePercentage: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#27ae60',
  },
  exportActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  exportButton: {
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  exportButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  featuresCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  dataSourcesCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  dataSourceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  dataSource: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
});
