import { Stack } from 'expo-router';

export default function AnalyticsLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Analytics & Reporting',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="dashboard" 
        options={{ 
          title: 'Analytics Dashboard',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="user-engagement" 
        options={{ 
          title: 'User Engagement',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="content-trends" 
        options={{ 
          title: 'Content Trends',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="financial-reports" 
        options={{ 
          title: 'Financial Reports',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="compliance-reports" 
        options={{ 
          title: 'Compliance Reports',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="engagement-detail/[id]" 
        options={{ 
          title: 'Engagement Detail',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="trend-detail/[id]" 
        options={{ 
          title: 'Trend Detail',
          headerShown: false 
        }} 
      />
    </Stack>
  );
}
