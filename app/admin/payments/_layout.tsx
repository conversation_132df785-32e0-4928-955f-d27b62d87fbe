import { Stack } from 'expo-router';

export default function PaymentsLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Payment Management',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="dashboard" 
        options={{ 
          title: 'Payment Dashboard',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="transactions" 
        options={{ 
          title: 'Transaction History',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="transaction-detail/[id]" 
        options={{ 
          title: 'Transaction Detail',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="verification" 
        options={{ 
          title: 'Payment Verification',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="refunds" 
        options={{ 
          title: 'Refund Management',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="subscriptions" 
        options={{ 
          title: 'Subscription Management',
          headerShown: false 
        }} 
      />
    </Stack>
  );
}
