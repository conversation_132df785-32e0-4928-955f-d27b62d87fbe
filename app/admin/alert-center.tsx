import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function AlertCenter() {
  const alerts = [
    {
      id: 1,
      priority: 'HIGH',
      category: 'Content Moderation',
      title: 'Multiple reports on Group #12345',
      description: 'Secret group flagged for potential illegal content',
      time: '5 minutes ago',
      color: '#e74c3c',
      route: '/admin/content/reported'
    },
    {
      id: 2,
      priority: 'MEDIUM',
      category: 'User Management',
      title: 'Verification backlog increasing',
      description: '150+ pending verification requests',
      time: '15 minutes ago',
      color: '#f39c12',
      route: '/admin/users/verification-requests'
    },
    {
      id: 3,
      priority: 'LOW',
      category: 'System',
      title: 'Scheduled maintenance reminder',
      description: 'Database optimization scheduled for tonight',
      time: '1 hour ago',
      color: '#3498db',
      route: '/admin/system/configuration'
    }
  ];

  const categories = [
    { name: 'Content Moderation', count: 12, color: '#e74c3c' },
    { name: 'User Management', count: 8, color: '#3498db' },
    { name: 'Group & Channel Management', count: 5, color: '#9b59b6' },
    { name: 'Payment Issues', count: 3, color: '#f39c12' },
    { name: 'System Alerts', count: 2, color: '#27ae60' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Alert Center</Text>
        <Text style={styles.subtitle}>Priority-Based Alert Management</Text>
        <Link href="/admin/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          The Alert Center provides a centralized view of all system alerts, prioritized by 
          severity and category. It enables quick identification and response to critical 
          issues across the MEENA platform.
        </Text>

        <Text style={styles.sectionTitle}>Active Alerts:</Text>
        {alerts.map((alert) => (
          <Link key={alert.id} href={alert.route} asChild>
            <TouchableOpacity style={[styles.alertCard, { borderLeftColor: alert.color }]}>
              <View style={styles.alertHeader}>
                <View style={styles.alertPriority}>
                  <Text style={[styles.priorityText, { color: alert.color }]}>
                    {alert.priority}
                  </Text>
                  <Text style={styles.categoryText}>{alert.category}</Text>
                </View>
                <Text style={styles.timeText}>{alert.time}</Text>
              </View>
              <Text style={styles.alertTitle}>{alert.title}</Text>
              <Text style={styles.alertDescription}>{alert.description}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Alert Categories:</Text>
        <View style={styles.categoriesGrid}>
          {categories.map((category, index) => (
            <View key={index} style={[styles.categoryCard, { borderTopColor: category.color }]}>
              <Text style={styles.categoryCount}>{category.count}</Text>
              <Text style={styles.categoryName}>{category.name}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Filter Options:</Text>
        <View style={styles.filterSection}>
          <TouchableOpacity style={styles.filterButton}>
            <Text style={styles.filterText}>Severity: All</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.filterButton}>
            <Text style={styles.filterText}>Category: All</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.filterButton}>
            <Text style={styles.filterText}>Time: 24h</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.quickActions}>
          <Link href="/admin/content/moderation-dashboard" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#e74c3c' }]}>
              <Text style={styles.actionButtonText}>Content Moderation</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/users/directory" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#3498db' }]}>
              <Text style={styles.actionButtonText}>User Management</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/groups/directory" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#9b59b6' }]}>
              <Text style={styles.actionButtonText}>Group Management</Text>
            </TouchableOpacity>
          </Link>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Alert selection → Specific alert details and resolution options
          {'\n'}• Content Moderation → Content Moderation Dashboard
          {'\n'}• User Management → User Directory Screen
          {'\n'}• Group & Channel Management → Group Management Dashboard
        </Text>

        <Text style={styles.sectionTitle}>Alert Management Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Priority-based alert system (High, Medium, Low)</Text>
          <Text style={styles.feature}>• Real-time alert notifications</Text>
          <Text style={styles.feature}>• Category-based filtering and organization</Text>
          <Text style={styles.feature}>• Quick action buttons for immediate response</Text>
          <Text style={styles.feature}>• Alert history and resolution tracking</Text>
          <Text style={styles.feature}>• Automated escalation for critical alerts</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e74c3c',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  alertCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  alertHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  alertPriority: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityText: {
    fontSize: 12,
    fontWeight: 'bold',
    marginRight: 8,
  },
  categoryText: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  timeText: {
    fontSize: 12,
    color: '#95a5a6',
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  alertDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  categoryCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderTopWidth: 4,
    alignItems: 'center',
  },
  categoryCount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  categoryName: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: 4,
  },
  filterSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  filterButton: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  filterText: {
    fontSize: 12,
    color: '#555',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  featuresList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
});
