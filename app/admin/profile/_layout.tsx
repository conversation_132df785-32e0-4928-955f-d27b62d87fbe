import { Stack } from 'expo-router';

export default function ProfileLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Admin Profile',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="settings" 
        options={{ 
          title: 'Profile Settings',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="security" 
        options={{ 
          title: 'Security Settings',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="2fa-setup" 
        options={{ 
          title: '2FA Setup',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="password-change" 
        options={{ 
          title: 'Change Password',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="activity-log" 
        options={{ 
          title: 'Activity Log',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="session-management" 
        options={{ 
          title: 'Session Management',
          headerShown: false 
        }} 
      />
    </Stack>
  );
}
