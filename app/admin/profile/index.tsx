import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function AdminProfile() {
  const adminData = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Senior Moderator',
    department: 'Content Moderation',
    joinDate: '2023-03-15',
    lastLogin: '2024-01-15 14:30:00',
    permissions: [
      'Content Moderation',
      'User Management',
      'Group Management',
      'Report Review',
      'User Banning'
    ]
  };

  const profileSections = [
    {
      title: 'Profile Settings',
      route: '/admin/profile/settings',
      color: '#3498db',
      description: 'Edit personal information and contact details',
      icon: '👤'
    },
    {
      title: 'Security Settings',
      route: '/admin/profile/security',
      color: '#e74c3c',
      description: 'Manage security protocols and authentication',
      icon: '🔒'
    },
    {
      title: 'Activity Log',
      route: '/admin/profile/activity-log',
      color: '#27ae60',
      description: 'View detailed history of admin actions',
      icon: '📋'
    },
    {
      title: 'Session Management',
      route: '/admin/profile/session-management',
      color: '#9b59b6',
      description: 'Manage active sessions and login history',
      icon: '🖥️'
    }
  ];

  const recentActivity = [
    {
      action: 'Banned User',
      details: '<NAME_EMAIL> for hate speech',
      timestamp: '2024-01-15 14:25:00',
      type: 'moderation'
    },
    {
      action: 'Approved Verification',
      details: 'Approved public <NAME_EMAIL>',
      timestamp: '2024-01-15 13:45:00',
      type: 'verification'
    },
    {
      action: 'Reviewed Content',
      details: 'Reviewed and removed inappropriate content in Group #12345',
      timestamp: '2024-01-15 12:30:00',
      type: 'content'
    },
    {
      action: 'Login',
      details: 'Logged in from IP: *************',
      timestamp: '2024-01-15 09:00:00',
      type: 'security'
    }
  ];

  const securityStatus = [
    { label: '2FA Enabled', status: true, color: '#27ae60' },
    { label: 'Strong Password', status: true, color: '#27ae60' },
    { label: 'Recent Login Review', status: true, color: '#27ae60' },
    { label: 'Session Timeout', status: true, color: '#27ae60' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Admin Profile</Text>
        <Text style={styles.subtitle}>Personal Information & Security</Text>
        <Link href="/admin/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          The Admin Profile screen provides comprehensive management of administrator 
          personal information, security settings, and activity tracking. It includes 
          role details, permissions, and quick access to security features.
        </Text>

        <Text style={styles.sectionTitle}>Profile Information:</Text>
        <View style={styles.profileCard}>
          <View style={styles.profileHeader}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>{adminData.name.charAt(0)}</Text>
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>{adminData.name}</Text>
              <Text style={styles.profileRole}>{adminData.role}</Text>
              <Text style={styles.profileDepartment}>{adminData.department}</Text>
            </View>
          </View>
          
          <View style={styles.profileDetails}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Email:</Text>
              <Text style={styles.detailValue}>{adminData.email}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Join Date:</Text>
              <Text style={styles.detailValue}>{adminData.joinDate}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Last Login:</Text>
              <Text style={styles.detailValue}>{adminData.lastLogin}</Text>
            </View>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Role Permissions:</Text>
        <View style={styles.permissionsCard}>
          <Text style={styles.permissionsTitle}>Current Permissions:</Text>
          {adminData.permissions.map((permission, index) => (
            <Text key={index} style={styles.permission}>• {permission}</Text>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Security Status:</Text>
        <View style={styles.securityCard}>
          {securityStatus.map((item, index) => (
            <View key={index} style={styles.securityItem}>
              <Text style={styles.securityLabel}>{item.label}</Text>
              <View style={styles.securityStatus}>
                <Text style={[styles.statusIndicator, { color: item.color }]}>
                  {item.status ? '✓' : '✗'}
                </Text>
                <Text style={[styles.statusText, { color: item.color }]}>
                  {item.status ? 'Active' : 'Inactive'}
                </Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Profile Management:</Text>
        {profileSections.map((section, index) => (
          <Link key={index} href={section.route} asChild>
            <TouchableOpacity style={[styles.sectionCard, { borderLeftColor: section.color }]}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionTitleContainer}>
                  <Text style={styles.sectionIcon}>{section.icon}</Text>
                  <Text style={[styles.sectionTitle, { color: section.color }]}>
                    {section.title}
                  </Text>
                </View>
              </View>
              <Text style={styles.sectionDescription}>{section.description}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Recent Activity:</Text>
        <View style={styles.activityContainer}>
          {recentActivity.map((activity, index) => (
            <View key={index} style={styles.activityItem}>
              <View style={styles.activityHeader}>
                <Text style={styles.activityAction}>{activity.action}</Text>
                <Text style={styles.activityTime}>{activity.timestamp}</Text>
              </View>
              <Text style={styles.activityDetails}>{activity.details}</Text>
              <Text style={[
                styles.activityType,
                { color: 
                  activity.type === 'moderation' ? '#e74c3c' :
                  activity.type === 'verification' ? '#3498db' :
                  activity.type === 'content' ? '#f39c12' : '#27ae60'
                }
              ]}>
                {activity.type.toUpperCase()}
              </Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.quickActions}>
          <Link href="/admin/profile/password-change" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#e74c3c' }]}>
              <Text style={styles.actionButtonText}>Change Password</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/profile/2fa-setup" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#27ae60' }]}>
              <Text style={styles.actionButtonText}>Setup 2FA</Text>
            </TouchableOpacity>
          </Link>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#95a5a6' }]}>
            <Text style={styles.actionButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Profile settings → Profile Settings Screen → Edit Interface
          {'\n'}• Security settings → Security Settings Screen → 2FA/Password
          {'\n'}• Activity log → Activity Log Screen → Detailed History
          {'\n'}• Session management → Session Management Screen → Active Sessions
        </Text>

        <Text style={styles.sectionTitle}>Profile Features:</Text>
        <View style={styles.featuresCard}>
          <Text style={styles.featureTitle}>Advanced Profile Management:</Text>
          <Text style={styles.feature}>• Comprehensive security settings and monitoring</Text>
          <Text style={styles.feature}>• Detailed activity logging and audit trails</Text>
          <Text style={styles.feature}>• Multi-factor authentication support</Text>
          <Text style={styles.feature}>• Session management and timeout controls</Text>
          <Text style={styles.feature}>• Role-based permission visualization</Text>
          <Text style={styles.feature}>• Personal information privacy controls</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#95a5a6',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  profileCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 8,
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#3498db',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  profileRole: {
    fontSize: 16,
    color: '#3498db',
    fontWeight: 'bold',
    marginTop: 4,
  },
  profileDepartment: {
    fontSize: 14,
    color: '#7f8c8d',
    marginTop: 2,
  },
  profileDetails: {
    borderTopWidth: 1,
    borderTopColor: '#ecf0f1',
    paddingTop: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  detailValue: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: 'bold',
  },
  permissionsCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  permissionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  permission: {
    fontSize: 14,
    color: '#555',
    marginBottom: 4,
  },
  securityCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  securityItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  securityLabel: {
    fontSize: 14,
    color: '#555',
  },
  securityStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    fontSize: 16,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  sectionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  sectionHeader: {
    marginBottom: 8,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  activityContainer: {
    marginBottom: 16,
  },
  activityItem: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityAction: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  activityTime: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  activityDetails: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  activityType: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  featuresCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
});
