import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link, useLocalSearchParams } from 'expo-router';

export default function GroupDetail() {
  const { id } = useLocalSearchParams();

  const groupData = {
    id: id as string,
    name: 'Tech Enthusiasts Community',
    type: 'Public',
    description: 'A community for technology enthusiasts to share knowledge and discuss latest trends',
    createdDate: '2023-06-15',
    memberCount: 1250,
    adminCount: 3,
    status: 'Active',
    inviteLink: 'https://meena.com/groups/tech-enthusiasts',
    aiRiskScore: 'Low',
    lastActivity: '2024-01-15 16:45:00'
  };

  const groupAdmins = [
    { name: '<PERSON>', email: '<EMAIL>', role: 'Creator', joinDate: '2023-06-15' },
    { name: '<PERSON>', email: '<EMAIL>', role: 'Admin', joinDate: '2023-07-20' },
    { name: '<PERSON>', email: '<EMAIL>', role: 'Moderator', joinDate: '2023-08-10' }
  ];

  const recentMessages = [
    {
      author: '<PERSON>',
      message: 'Check out this new AI framework that was just released!',
      timestamp: '2024-01-15 16:45:00',
      flagged: false
    },
    {
      author: '<PERSON> <PERSON>',
      message: 'Has anyone tried the new React 19 features?',
      timestamp: '2024-01-15 15:30:00',
      flagged: false
    },
    {
      author: 'Charlie <PERSON>',
      message: 'This content has been flagged by AI for review',
      timestamp: '2024-01-15 14:20:00',
      flagged: true
    }
  ];

  const sharedMedia = [
    { type: 'Image', name: 'tech-conference-2024.jpg', uploadedBy: 'Alice Brown', aiRisk: 'Low' },
    { type: 'Document', name: 'react-best-practices.pdf', uploadedBy: 'Bob Wilson', aiRisk: 'Low' },
    { type: 'Video', name: 'ai-demo-presentation.mp4', uploadedBy: 'Charlie Davis', aiRisk: 'Medium' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Group Detail</Text>
        <Text style={styles.subtitle}>{groupData.name}</Text>
        <Link href="/admin/groups/directory" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Directory</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          The Group Detail Screen provides comprehensive information about a specific 
          group, including member management, content moderation tools, and administrative 
          controls for group oversight.
        </Text>

        <Text style={styles.sectionTitle}>Group Information:</Text>
        <View style={styles.infoCard}>
          <View style={styles.groupHeader}>
            <View style={styles.groupIcon}>
              <Text style={styles.groupInitial}>{groupData.name.charAt(0)}</Text>
            </View>
            <View style={styles.groupInfo}>
              <Text style={styles.groupName}>{groupData.name}</Text>
              <Text style={styles.groupType}>{groupData.type} Group</Text>
              <View style={[
                styles.statusBadge,
                { backgroundColor: groupData.status === 'Active' ? '#27ae60' : '#e74c3c' }
              ]}>
                <Text style={styles.statusText}>{groupData.status}</Text>
              </View>
            </View>
          </View>
          
          <Text style={styles.groupDescription}>{groupData.description}</Text>
          
          <View style={styles.detailsGrid}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Group ID:</Text>
              <Text style={styles.detailValue}>{groupData.id}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Created:</Text>
              <Text style={styles.detailValue}>{groupData.createdDate}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Members:</Text>
              <Text style={styles.detailValue}>{groupData.memberCount}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Admins:</Text>
              <Text style={styles.detailValue}>{groupData.adminCount}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>AI Risk Score:</Text>
              <Text style={[
                styles.detailValue,
                { color: groupData.aiRiskScore === 'Low' ? '#27ae60' : '#f39c12' }
              ]}>
                {groupData.aiRiskScore}
              </Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Last Activity:</Text>
              <Text style={styles.detailValue}>{groupData.lastActivity}</Text>
            </View>
          </View>

          {groupData.type === 'Public' && (
            <View style={styles.inviteLinkContainer}>
              <Text style={styles.inviteLinkLabel}>Invite Link:</Text>
              <Text style={styles.inviteLink}>{groupData.inviteLink}</Text>
            </View>
          )}
        </View>

        <Text style={styles.sectionTitle}>Group Administrators:</Text>
        <View style={styles.adminsContainer}>
          {groupAdmins.map((admin, index) => (
            <View key={index} style={styles.adminCard}>
              <View style={styles.adminInfo}>
                <Text style={styles.adminName}>{admin.name}</Text>
                <Text style={styles.adminEmail}>{admin.email}</Text>
                <Text style={styles.adminJoinDate}>Joined: {admin.joinDate}</Text>
              </View>
              <View style={styles.adminRole}>
                <Text style={[
                  styles.roleText,
                  { color: admin.role === 'Creator' ? '#e74c3c' : '#3498db' }
                ]}>
                  {admin.role}
                </Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Recent Messages:</Text>
        <View style={styles.messagesContainer}>
          {recentMessages.map((message, index) => (
            <View key={index} style={[
              styles.messageCard,
              message.flagged && styles.flaggedMessage
            ]}>
              <View style={styles.messageHeader}>
                <Text style={styles.messageAuthor}>{message.author}</Text>
                <Text style={styles.messageTime}>{message.timestamp}</Text>
                {message.flagged && <Text style={styles.flaggedText}>⚠️ FLAGGED</Text>}
              </View>
              <Text style={styles.messageText}>{message.message}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Shared Media & Files:</Text>
        <View style={styles.mediaContainer}>
          {sharedMedia.map((media, index) => (
            <View key={index} style={styles.mediaCard}>
              <View style={styles.mediaInfo}>
                <Text style={styles.mediaType}>{media.type}</Text>
                <Text style={styles.mediaName}>{media.name}</Text>
                <Text style={styles.mediaUploader}>By: {media.uploadedBy}</Text>
              </View>
              <View style={styles.mediaRisk}>
                <Text style={[
                  styles.riskText,
                  { color: media.aiRisk === 'Low' ? '#27ae60' : '#f39c12' }
                ]}>
                  {media.aiRisk} Risk
                </Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Moderation Panel:</Text>
        <View style={styles.moderationPanel}>
          <TouchableOpacity style={[styles.moderationButton, { backgroundColor: '#3498db' }]}>
            <Text style={styles.moderationButtonText}>Review Content</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.moderationButton, { backgroundColor: '#f39c12' }]}>
            <Text style={styles.moderationButtonText}>Warn Creator</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.moderationButton, { backgroundColor: '#e74c3c' }]}>
            <Text style={styles.moderationButtonText}>Ban Group</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <Text style={styles.flowDescription}>
          • Back → Group Directory Screen
          {'\n'}• Verification → Group Verification Screen
          {'\n'}• Content moderation → Content Moderation Screen
          {'\n'}• Group action → Group Action Screen
          {'\n'}• Member management → Individual member profiles
        </Text>

        <Text style={styles.sectionTitle}>Group Management Features:</Text>
        <View style={styles.featuresCard}>
          <Text style={styles.featureTitle}>Comprehensive Group Oversight:</Text>
          <Text style={styles.feature}>• Real-time member activity monitoring</Text>
          <Text style={styles.feature}>• AI-powered content risk assessment</Text>
          <Text style={styles.feature}>• Administrative action logging</Text>
          <Text style={styles.feature}>• Bulk member management operations</Text>
          <Text style={styles.feature}>• Content search and filtering</Text>
          <Text style={styles.feature}>• Automated violation detection</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#9b59b6',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  infoCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  groupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  groupIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#9b59b6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  groupInitial: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  groupType: {
    fontSize: 14,
    color: '#7f8c8d',
    marginTop: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginTop: 8,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  groupDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    marginBottom: 16,
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  detailItem: {
    width: '48%',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: 'bold',
  },
  inviteLinkContainer: {
    borderTopWidth: 1,
    borderTopColor: '#ecf0f1',
    paddingTop: 16,
  },
  inviteLinkLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    marginBottom: 4,
  },
  inviteLink: {
    fontSize: 14,
    color: '#3498db',
    fontWeight: 'bold',
  },
  adminsContainer: {
    marginBottom: 16,
  },
  adminCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  adminInfo: {
    flex: 1,
  },
  adminName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  adminEmail: {
    fontSize: 14,
    color: '#7f8c8d',
    marginTop: 2,
  },
  adminJoinDate: {
    fontSize: 12,
    color: '#95a5a6',
    marginTop: 2,
  },
  adminRole: {
    alignItems: 'flex-end',
  },
  roleText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  messagesContainer: {
    marginBottom: 16,
  },
  messageCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  flaggedMessage: {
    borderLeftWidth: 4,
    borderLeftColor: '#e74c3c',
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  messageAuthor: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  messageTime: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  flaggedText: {
    fontSize: 12,
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  messageText: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  mediaContainer: {
    marginBottom: 16,
  },
  mediaCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  mediaInfo: {
    flex: 1,
  },
  mediaType: {
    fontSize: 12,
    color: '#7f8c8d',
    marginBottom: 2,
  },
  mediaName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  mediaUploader: {
    fontSize: 12,
    color: '#95a5a6',
    marginTop: 2,
  },
  mediaRisk: {
    alignItems: 'flex-end',
  },
  riskText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  moderationPanel: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  moderationButton: {
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  moderationButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  featuresCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
});
