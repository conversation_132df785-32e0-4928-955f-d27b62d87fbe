import { Stack } from 'expo-router';

export default function GroupsLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Group & Channel Management',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="management-dashboard" 
        options={{ 
          title: 'Group Management Dashboard',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="directory" 
        options={{ 
          title: 'Group Directory',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="[id]" 
        options={{ 
          title: 'Group Detail',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="creation-requests" 
        options={{ 
          title: 'Group Creation Requests',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="secret-monitoring" 
        options={{ 
          title: 'Secret Groups Monitoring',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="banned-groups" 
        options={{ 
          title: 'Banned Groups',
          headerShown: false 
        }} 
      />
    </Stack>
  );
}
