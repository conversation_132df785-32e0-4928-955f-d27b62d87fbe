import { Stack, useLocalSearchParams } from 'expo-router';
import React from 'react';
import { View, Text } from 'react-native';

const DetailScreen = () => {
  const { id } = useLocalSearchParams();
  const screenName = "Content Detail Screen";

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Stack.Screen options={{ title: `${screenName} ${id}` }} />
      <Text>{screenName} for ID: {id}</Text>
    </View>
  );
};

export default DetailScreen;
