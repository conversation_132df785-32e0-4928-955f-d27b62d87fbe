import { Stack } from 'expo-router';

export default function ContentLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Content Moderation',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="moderation-dashboard" 
        options={{ 
          title: 'Content Moderation Dashboard',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="reported" 
        options={{ 
          title: 'Reported Content',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="automatic-detection" 
        options={{ 
          title: 'Automatic Detection Alerts',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="manual-review" 
        options={{ 
          title: 'Manual Review Queue',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="content-detail/[id]" 
        options={{ 
          title: 'Content Detail',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="policy-reference" 
        options={{ 
          title: 'Policy Reference',
          headerShown: false 
        }} 
      />
    </Stack>
  );
}
