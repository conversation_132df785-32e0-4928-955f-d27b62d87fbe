import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function ContentModerationDashboard() {
  const contentQueues = [
    {
      title: 'Reported Content',
      count: 89,
      priority: 'HIGH',
      color: '#e74c3c',
      route: '/admin/content/reported',
      description: 'User-reported content requiring review'
    },
    {
      title: 'Automatic Detection Alerts',
      count: 156,
      priority: 'MEDIUM',
      color: '#f39c12',
      route: '/admin/content/automatic-detection',
      description: 'AI-flagged content for human verification'
    },
    {
      title: 'Manual Review Queue',
      count: 23,
      priority: 'HIGH',
      color: '#e67e22',
      route: '/admin/content/manual-review',
      description: 'Content requiring manual moderator review'
    },
    {
      title: 'Policy Violations',
      count: 45,
      priority: 'MEDIUM',
      color: '#9b59b6',
      route: '/admin/content/policy-violations',
      description: 'Content violating community guidelines'
    }
  ];

  const violationCategories = [
    { name: 'Racism', count: 12, severity: 'HIGH' },
    { name: 'Sexual Content', count: 34, severity: 'HIGH' },
    { name: 'Threats', count: 8, severity: 'HIGH' },
    { name: 'Violence and Prejudices', count: 15, severity: 'HIGH' },
    { name: 'Hate Speech', count: 19, severity: 'HIGH' },
    { name: 'Terrorism', count: 2, severity: 'CRITICAL' },
    { name: 'Harassment', count: 28, severity: 'MEDIUM' },
    { name: 'Spam', count: 67, severity: 'LOW' }
  ];

  const moderationStats = [
    { label: 'Content Processed (24h)', value: '2,345', color: '#27ae60' },
    { label: 'Actions Taken', value: '156', color: '#e74c3c' },
    { label: 'False Positives', value: '23', color: '#f39c12' },
    { label: 'Pending Review', value: '89', color: '#3498db' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Content Moderation Dashboard</Text>
        <Text style={styles.subtitle}>Comprehensive Content Review System</Text>
        <Link href="/admin/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          The Content Moderation Dashboard provides a centralized overview of all content 
          requiring review. It implements a two-tier moderation system with automatic 
          detection for public/private content and manual review for secret groups/channels.
        </Text>

        <Text style={styles.sectionTitle}>Moderation Statistics:</Text>
        <View style={styles.statsGrid}>
          {moderationStats.map((stat, index) => (
            <View key={index} style={[styles.statCard, { borderTopColor: stat.color }]}>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Content Review Queues:</Text>
        {contentQueues.map((queue, index) => (
          <Link key={index} href={queue.route} asChild>
            <TouchableOpacity style={[styles.queueCard, { borderLeftColor: queue.color }]}>
              <View style={styles.queueHeader}>
                <Text style={[styles.queueTitle, { color: queue.color }]}>{queue.title}</Text>
                <View style={styles.queueMeta}>
                  <Text style={[styles.priorityBadge, { backgroundColor: queue.color }]}>
                    {queue.priority}
                  </Text>
                  <Text style={styles.queueCount}>{queue.count}</Text>
                </View>
              </View>
              <Text style={styles.queueDescription}>{queue.description}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Violation Categories:</Text>
        <View style={styles.violationsGrid}>
          {violationCategories.map((category, index) => (
            <View key={index} style={[
              styles.violationCard,
              { borderLeftColor: category.severity === 'CRITICAL' ? '#8e44ad' : 
                                 category.severity === 'HIGH' ? '#e74c3c' :
                                 category.severity === 'MEDIUM' ? '#f39c12' : '#27ae60' }
            ]}>
              <Text style={styles.violationName}>{category.name}</Text>
              <Text style={styles.violationCount}>{category.count} cases</Text>
              <Text style={[
                styles.violationSeverity,
                { color: category.severity === 'CRITICAL' ? '#8e44ad' : 
                         category.severity === 'HIGH' ? '#e74c3c' :
                         category.severity === 'MEDIUM' ? '#f39c12' : '#27ae60' }
              ]}>
                {category.severity}
              </Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Moderation System Features:</Text>
        <View style={styles.featuresCard}>
          <Text style={styles.featureTitle}>Two-Tier Moderation System:</Text>
          <Text style={styles.feature}>• Automatic moderation for public/private groups/channels</Text>
          <Text style={styles.feature}>• Manual review for secret groups/channels</Text>
          <Text style={styles.feature}>• AI-powered content detection and classification</Text>
          <Text style={styles.feature}>• Real-time alert system for critical violations</Text>
          <Text style={styles.feature}>• Comprehensive audit trail for all actions</Text>
          <Text style={styles.feature}>• Appeal process for moderation decisions</Text>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.quickActions}>
          <Link href="/admin/content/reported" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#e74c3c' }]}>
              <Text style={styles.actionButtonText}>Review Reports</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/content/automatic-detection" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#f39c12' }]}>
              <Text style={styles.actionButtonText}>AI Alerts</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/content/policy-reference" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#3498db' }]}>
              <Text style={styles.actionButtonText}>Policy Guide</Text>
            </TouchableOpacity>
          </Link>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Reported content → Reported Content Screen → Content Detail Screen
          {'\n'}• Automatic detection alerts → Detection Alerts Screen → Content Review
          {'\n'}• Manual review queue → Manual Review Screen → Policy Reference
          {'\n'}• Policy violation statistics → Violation Statistics Screen
        </Text>

        <Text style={styles.sectionTitle}>Enforcement Policies:</Text>
        <View style={styles.policiesCard}>
          <Text style={styles.policyTitle}>Differentiated Enforcement:</Text>
          <Text style={styles.policy}>• Automatic bans for public/private groups violating policies</Text>
          <Text style={styles.policy}>• Manual review for secret groups with potentially illegal content</Text>
          <Text style={styles.policy}>• Escalation procedures for terrorism and violent extremism</Text>
          <Text style={styles.policy}>• User notification system for all moderation actions</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e74c3c',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderTopWidth: 4,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  statLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: 4,
  },
  queueCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  queueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  queueTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  queueMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityBadge: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginRight: 8,
  },
  queueCount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  queueDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  violationsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  violationCard: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 6,
    width: '48%',
    marginBottom: 8,
    borderLeftWidth: 3,
  },
  violationName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  violationCount: {
    fontSize: 12,
    color: '#7f8c8d',
    marginTop: 2,
  },
  violationSeverity: {
    fontSize: 10,
    fontWeight: 'bold',
    marginTop: 4,
  },
  featuresCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  policiesCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  policyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  policy: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
});
