import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link, useLocalSearchParams } from 'expo-router';

export default function UserDetail() {
  const { id } = useLocalSearchParams();

  const userData = {
    id: id as string,
    pid: 'PID12345',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+**********',
    status: 'Active',
    goldMember: true,
    verification: 'Public',
    country: 'USA',
    joinDate: '2023-01-15',
    lastLogin: '2024-01-15 14:30:00',
    deviceType: 'iPhone 15 Pro',
    location: 'New York, USA'
  };

  const tabs = [
    { id: 'basic', name: 'Basic Info', active: true },
    { id: 'account', name: 'Account Status', active: false },
    { id: 'groups', name: 'Groups & Channels', active: false },
    { id: 'content', name: 'Content History', active: false },
    { id: 'activity', name: 'Activity Log', active: false }
  ];

  const groups = [
    { name: 'Tech Enthusiasts', type: 'Public', created: '2023-02-01', members: 1250 },
    { name: 'Photography Club', type: 'Private', created: '2023-03-15', members: 89 },
    { name: 'Secret Group Alpha', type: 'Secret', created: '2023-04-20', members: 12, flagged: true }
  ];

  const recentActivity = [
    { action: 'Login', timestamp: '2024-01-15 14:30:00', device: 'iPhone 15 Pro' },
    { action: 'Created Group', timestamp: '2024-01-14 09:15:00', details: 'Photography Club' },
    { action: 'Posted Content', timestamp: '2024-01-13 16:45:00', details: 'Image post in Tech Enthusiasts' },
    { action: 'Reported User', timestamp: '2024-01-12 11:20:00', details: 'Spam report against user456' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>User Detail</Text>
        <Text style={styles.subtitle}>{userData.name} ({userData.pid})</Text>
        <Link href="/admin/users/directory" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Directory</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          The User Detail Screen provides comprehensive information about a specific user, 
          organized in tabs for easy navigation. It includes moderation tools and action 
          buttons for user management.
        </Text>

        <Text style={styles.sectionTitle}>User Information Tabs:</Text>
        <View style={styles.tabsContainer}>
          {tabs.map((tab) => (
            <TouchableOpacity 
              key={tab.id} 
              style={[styles.tab, tab.active && styles.activeTab]}
            >
              <Text style={[styles.tabText, tab.active && styles.activeTabText]}>
                {tab.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Basic Information:</Text>
        <View style={styles.infoCard}>
          <View style={styles.profileHeader}>
            <View style={styles.profilePhoto}>
              <Text style={styles.profileInitial}>{userData.name.charAt(0)}</Text>
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>{userData.name}</Text>
              <Text style={styles.profileBio}>Software Developer & Tech Enthusiast</Text>
            </View>
          </View>
          
          <View style={styles.detailsGrid}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>User ID:</Text>
              <Text style={styles.detailValue}>{userData.id}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>PID:</Text>
              <Text style={styles.detailValue}>{userData.pid}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Email:</Text>
              <Text style={styles.detailValue}>{userData.email}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Phone:</Text>
              <Text style={styles.detailValue}>{userData.phone}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Join Date:</Text>
              <Text style={styles.detailValue}>{userData.joinDate}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Last Login:</Text>
              <Text style={styles.detailValue}>{userData.lastLogin}</Text>
            </View>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Account Status:</Text>
        <View style={styles.statusCard}>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>Status:</Text>
            <View style={[styles.statusBadge, { backgroundColor: '#27ae60' }]}>
              <Text style={styles.statusText}>{userData.status}</Text>
            </View>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>Gold Member:</Text>
            <Text style={styles.statusValue}>{userData.goldMember ? 'Yes' : 'No'}</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>Verification:</Text>
            <Text style={styles.statusValue}>{userData.verification}</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>2FA Enabled:</Text>
            <Text style={styles.statusValue}>Yes</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Groups & Channels:</Text>
        {groups.map((group, index) => (
          <View key={index} style={[styles.groupCard, group.flagged && styles.flaggedGroup]}>
            <View style={styles.groupHeader}>
              <Text style={styles.groupName}>{group.name}</Text>
              <Text style={styles.groupType}>{group.type}</Text>
              {group.flagged && <Text style={styles.flaggedText}>⚠️ FLAGGED</Text>}
            </View>
            <Text style={styles.groupDetails}>
              Created: {group.created} | Members: {group.members}
            </Text>
          </View>
        ))}

        <Text style={styles.sectionTitle}>Recent Activity:</Text>
        <View style={styles.activityList}>
          {recentActivity.map((activity, index) => (
            <View key={index} style={styles.activityItem}>
              <Text style={styles.activityAction}>{activity.action}</Text>
              <Text style={styles.activityTime}>{activity.timestamp}</Text>
              {activity.details && (
                <Text style={styles.activityDetails}>{activity.details}</Text>
              )}
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Moderation Actions:</Text>
        <View style={styles.moderationPanel}>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#f39c12' }]}>
            <Text style={styles.actionButtonText}>Warn & Notify</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#e74c3c' }]}>
            <Text style={styles.actionButtonText}>Temporary Ban</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#8e44ad' }]}>
            <Text style={styles.actionButtonText}>Permanent Ban</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <Text style={styles.flowDescription}>
          • Ban action → Confirmation modal → Action logged
          {'\n'}• Groups/Channels → Group/Channel detail screens
          {'\n'}• Content History → Searchable content logs
          {'\n'}• Activity Log → Detailed user activity timeline
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#2980b9',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  tabsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  tab: {
    backgroundColor: '#ecf0f1',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
  },
  activeTab: {
    backgroundColor: '#3498db',
  },
  tabText: {
    fontSize: 12,
    color: '#555',
  },
  activeTabText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  infoCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  profilePhoto: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#3498db',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileInitial: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  profileBio: {
    fontSize: 14,
    color: '#7f8c8d',
    marginTop: 4,
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  detailItem: {
    width: '48%',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: 'bold',
  },
  statusCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusLabel: {
    fontSize: 14,
    color: '#555',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  statusValue: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: 'bold',
  },
  groupCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  flaggedGroup: {
    borderLeftWidth: 4,
    borderLeftColor: '#e74c3c',
  },
  groupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  groupName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  groupType: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  flaggedText: {
    fontSize: 12,
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  groupDetails: {
    fontSize: 12,
    color: '#555',
  },
  activityList: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 16,
  },
  activityItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ecf0f1',
  },
  activityAction: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  activityTime: {
    fontSize: 12,
    color: '#7f8c8d',
    marginTop: 2,
  },
  activityDetails: {
    fontSize: 12,
    color: '#555',
    marginTop: 4,
  },
  moderationPanel: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
});
