import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function SupportDashboard() {
  const supportCategories = [
    {
      title: 'Ticket Management',
      route: '/admin/support/tickets',
      color: '#3498db',
      description: 'Manage user support tickets and resolution tracking',
      count: 45,
      priority: 'HIGH'
    },
    {
      title: 'Knowledge Base',
      route: '/admin/support/knowledge-base',
      color: '#27ae60',
      description: 'Searchable help documentation and article management',
      count: 127,
      priority: 'MEDIUM'
    },
    {
      title: 'Feature Requests',
      route: '/admin/support/feature-requests',
      color: '#9b59b6',
      description: 'User-submitted feature requests with voting system',
      count: 23,
      priority: 'LOW'
    },
    {
      title: 'User Inquiries',
      route: '/admin/support/user-inquiries',
      color: '#e67e22',
      description: 'General user questions and technical issues',
      count: 67,
      priority: 'MEDIUM'
    }
  ];

  const ticketStats = [
    { label: 'Open Tickets', value: '45', color: '#e74c3c' },
    { label: 'In Progress', value: '23', color: '#f39c12' },
    { label: 'Resolved Today', value: '18', color: '#27ae60' },
    { label: 'Avg Response Time', value: '2.4h', color: '#3498db' }
  ];

  const recentTickets = [
    {
      id: 'TK-001',
      subject: 'Cannot access Gold membership features',
      user: '<EMAIL>',
      priority: 'HIGH',
      status: 'Open',
      created: '2 hours ago'
    },
    {
      id: 'TK-002',
      subject: 'Group creation payment failed',
      user: '<EMAIL>',
      priority: 'MEDIUM',
      status: 'In Progress',
      created: '4 hours ago'
    },
    {
      id: 'TK-003',
      subject: 'Account recovery assistance needed',
      user: '<EMAIL>',
      priority: 'HIGH',
      status: 'Open',
      created: '6 hours ago'
    }
  ];

  const agentPerformance = [
    { name: 'Sarah Johnson', tickets: 12, avgTime: '1.8h', rating: 4.9 },
    { name: 'Mike Chen', tickets: 8, avgTime: '2.1h', rating: 4.7 },
    { name: 'Lisa Rodriguez', tickets: 15, avgTime: '2.3h', rating: 4.8 }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Support Dashboard</Text>
        <Text style={styles.subtitle}>Comprehensive Customer Support System</Text>
        <Link href="/admin/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          The Support Dashboard provides a centralized view of all customer support 
          activities, including ticket management, knowledge base administration, 
          feature requests, and agent performance tracking.
        </Text>

        <Text style={styles.sectionTitle}>Support Statistics:</Text>
        <View style={styles.statsGrid}>
          {ticketStats.map((stat, index) => (
            <View key={index} style={[styles.statCard, { borderTopColor: stat.color }]}>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Support Categories:</Text>
        {supportCategories.map((category, index) => (
          <Link key={index} href={category.route} asChild>
            <TouchableOpacity style={[styles.categoryCard, { borderLeftColor: category.color }]}>
              <View style={styles.categoryHeader}>
                <Text style={[styles.categoryTitle, { color: category.color }]}>
                  {category.title}
                </Text>
                <View style={styles.categoryMeta}>
                  <Text style={[
                    styles.priorityBadge,
                    { backgroundColor: 
                      category.priority === 'HIGH' ? '#e74c3c' :
                      category.priority === 'MEDIUM' ? '#f39c12' : '#27ae60'
                    }
                  ]}>
                    {category.priority}
                  </Text>
                  <Text style={styles.categoryCount}>{category.count}</Text>
                </View>
              </View>
              <Text style={styles.categoryDescription}>{category.description}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Recent Tickets:</Text>
        <View style={styles.ticketsContainer}>
          {recentTickets.map((ticket) => (
            <Link key={ticket.id} href={`/admin/support/ticket-detail/${ticket.id}`} asChild>
              <TouchableOpacity style={styles.ticketCard}>
                <View style={styles.ticketHeader}>
                  <Text style={styles.ticketId}>{ticket.id}</Text>
                  <View style={styles.ticketMeta}>
                    <Text style={[
                      styles.ticketPriority,
                      { color: ticket.priority === 'HIGH' ? '#e74c3c' : '#f39c12' }
                    ]}>
                      {ticket.priority}
                    </Text>
                    <Text style={[
                      styles.ticketStatus,
                      { color: ticket.status === 'Open' ? '#e74c3c' : '#f39c12' }
                    ]}>
                      {ticket.status}
                    </Text>
                  </View>
                </View>
                <Text style={styles.ticketSubject}>{ticket.subject}</Text>
                <View style={styles.ticketFooter}>
                  <Text style={styles.ticketUser}>{ticket.user}</Text>
                  <Text style={styles.ticketTime}>{ticket.created}</Text>
                </View>
              </TouchableOpacity>
            </Link>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Agent Performance:</Text>
        <View style={styles.agentPerformanceCard}>
          <Text style={styles.performanceTitle}>Top Performing Agents (This Week):</Text>
          {agentPerformance.map((agent, index) => (
            <View key={index} style={styles.agentItem}>
              <View style={styles.agentInfo}>
                <Text style={styles.agentName}>{agent.name}</Text>
                <Text style={styles.agentStats}>
                  {agent.tickets} tickets • {agent.avgTime} avg time
                </Text>
              </View>
              <View style={styles.agentRating}>
                <Text style={styles.ratingValue}>⭐ {agent.rating}</Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.quickActions}>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#e74c3c' }]}>
            <Text style={styles.actionButtonText}>Create Ticket</Text>
          </TouchableOpacity>
          <Link href="/admin/support/knowledge-base" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#27ae60' }]}>
              <Text style={styles.actionButtonText}>Add Article</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/support/user-communication" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#3498db' }]}>
              <Text style={styles.actionButtonText}>Send Broadcast</Text>
            </TouchableOpacity>
          </Link>
        </View>

        <Text style={styles.sectionTitle}>Support Features:</Text>
        <View style={styles.featuresCard}>
          <Text style={styles.featureTitle}>Comprehensive Support System:</Text>
          <Text style={styles.feature}>• Multi-channel ticket management system</Text>
          <Text style={styles.feature}>• Searchable knowledge base with categories</Text>
          <Text style={styles.feature}>• Automated ticket routing and prioritization</Text>
          <Text style={styles.feature}>• Agent performance tracking and analytics</Text>
          <Text style={styles.feature}>• User satisfaction surveys and feedback</Text>
          <Text style={styles.feature}>• Integration with user management system</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Ticket management → Ticket Management Screen → Ticket Detail
          {'\n'}• Knowledge base → Knowledge Base Screen → Article Detail
          {'\n'}• Feature requests → Feature Requests Screen → Request Detail
          {'\n'}• User inquiries → User Inquiries Screen → Inquiry Resolution
        </Text>

        <Text style={styles.sectionTitle}>Support Metrics:</Text>
        <View style={styles.metricsCard}>
          <Text style={styles.metricsTitle}>Key Performance Indicators:</Text>
          <Text style={styles.metric}>• First Response Time: 2.4 hours average</Text>
          <Text style={styles.metric}>• Resolution Time: 18.6 hours average</Text>
          <Text style={styles.metric}>• Customer Satisfaction: 4.7/5.0 rating</Text>
          <Text style={styles.metric}>• Ticket Volume: 45 open, 156 resolved this week</Text>
          <Text style={styles.metric}>• Knowledge Base Usage: 89% self-service rate</Text>
          <Text style={styles.metric}>• Agent Utilization: 85% average capacity</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e67e22',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderTopWidth: 4,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  statLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: 4,
  },
  categoryCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  categoryMeta: {
    alignItems: 'flex-end',
  },
  priorityBadge: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginBottom: 4,
  },
  categoryCount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  categoryDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  ticketsContainer: {
    marginBottom: 16,
  },
  ticketCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  ticketHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  ticketId: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  ticketMeta: {
    flexDirection: 'row',
    gap: 8,
  },
  ticketPriority: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  ticketStatus: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  ticketSubject: {
    fontSize: 16,
    color: '#2c3e50',
    marginBottom: 8,
  },
  ticketFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ticketUser: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  ticketTime: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  agentPerformanceCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  performanceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  agentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  agentInfo: {
    flex: 1,
  },
  agentName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  agentStats: {
    fontSize: 12,
    color: '#7f8c8d',
    marginTop: 2,
  },
  agentRating: {
    alignItems: 'flex-end',
  },
  ratingValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#f39c12',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  featuresCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  metricsCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  metricsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  metric: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
});
