import { Stack } from 'expo-router';

export default function SupportLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Support & Helpdesk',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="dashboard" 
        options={{ 
          title: 'Support Dashboard',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="tickets" 
        options={{ 
          title: 'Ticket Management',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="ticket-detail/[id]" 
        options={{ 
          title: 'Ticket Detail',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="knowledge-base" 
        options={{ 
          title: 'Knowledge Base',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="article/[id]" 
        options={{ 
          title: 'Article Detail',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="feature-requests" 
        options={{ 
          title: 'Feature Requests',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="user-inquiries" 
        options={{ 
          title: 'User Inquiries',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="user-communication" 
        options={{ 
          title: 'User Communication',
          headerShown: false 
        }} 
      />
    </Stack>
  );
}
