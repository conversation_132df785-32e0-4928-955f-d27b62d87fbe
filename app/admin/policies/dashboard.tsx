import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function PolicyDashboard() {
  const policyCategories = [
    {
      title: 'Community Rules',
      route: '/admin/policies/community-rules',
      color: '#e74c3c',
      description: 'Platform behavior guidelines and content standards',
      lastUpdated: '2024-01-10',
      status: 'Active',
      violations: 234
    },
    {
      title: 'Terms of Use',
      route: '/admin/policies/terms-of-use',
      color: '#3498db',
      description: 'Legal terms and conditions for platform usage',
      lastUpdated: '2023-12-15',
      status: 'Active',
      violations: 45
    },
    {
      title: 'Privacy Policy',
      route: '/admin/policies/privacy-policy',
      color: '#27ae60',
      description: 'Data collection, usage, and protection policies',
      lastUpdated: '2024-01-05',
      status: 'Active',
      violations: 12
    },
    {
      title: 'Intellectual Property',
      route: '/admin/policies/intellectual-property',
      color: '#9b59b6',
      description: 'Copyright, trademark, and IP protection guidelines',
      lastUpdated: '2023-11-20',
      status: 'Active',
      violations: 89
    }
  ];

  const complianceStats = [
    { label: 'Total Policies', value: '12', color: '#3498db' },
    { label: 'Active Policies', value: '12', color: '#27ae60' },
    { label: 'Pending Updates', value: '3', color: '#f39c12' },
    { label: 'Violations (30d)', value: '380', color: '#e74c3c' }
  ];

  const recentUpdates = [
    {
      policy: 'Community Rules',
      change: 'Added AI-generated content guidelines',
      date: '2024-01-10',
      author: 'Legal Team'
    },
    {
      policy: 'Privacy Policy',
      change: 'Updated data retention periods',
      date: '2024-01-05',
      author: 'Compliance Officer'
    },
    {
      policy: 'Terms of Use',
      change: 'Clarified payment refund policies',
      date: '2023-12-15',
      author: 'Legal Team'
    }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Policy Dashboard</Text>
        <Text style={styles.subtitle}>Comprehensive Policy Management System</Text>
        <Link href="/admin/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          The Policy Dashboard provides centralized management of all platform policies, 
          including community rules, terms of use, privacy policies, and intellectual 
          property guidelines. It tracks compliance statistics and policy effectiveness.
        </Text>

        <Text style={styles.sectionTitle}>Compliance Statistics:</Text>
        <View style={styles.statsGrid}>
          {complianceStats.map((stat, index) => (
            <View key={index} style={[styles.statCard, { borderTopColor: stat.color }]}>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Policy Categories:</Text>
        {policyCategories.map((policy, index) => (
          <Link key={index} href={policy.route} asChild>
            <TouchableOpacity style={[styles.policyCard, { borderLeftColor: policy.color }]}>
              <View style={styles.policyHeader}>
                <Text style={[styles.policyTitle, { color: policy.color }]}>{policy.title}</Text>
                <View style={styles.policyMeta}>
                  <Text style={[styles.statusBadge, { backgroundColor: '#27ae60' }]}>
                    {policy.status}
                  </Text>
                  <Text style={styles.violationCount}>{policy.violations} violations</Text>
                </View>
              </View>
              <Text style={styles.policyDescription}>{policy.description}</Text>
              <Text style={styles.lastUpdated}>Last updated: {policy.lastUpdated}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Recent Policy Updates:</Text>
        <View style={styles.updatesContainer}>
          {recentUpdates.map((update, index) => (
            <View key={index} style={styles.updateCard}>
              <View style={styles.updateHeader}>
                <Text style={styles.updatePolicy}>{update.policy}</Text>
                <Text style={styles.updateDate}>{update.date}</Text>
              </View>
              <Text style={styles.updateChange}>{update.change}</Text>
              <Text style={styles.updateAuthor}>By: {update.author}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.quickActions}>
          <Link href="/admin/policies/violation-examples" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#e74c3c' }]}>
              <Text style={styles.actionButtonText}>Violation Examples</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/policies/implementation" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#3498db' }]}>
              <Text style={styles.actionButtonText}>Implementation</Text>
            </TouchableOpacity>
          </Link>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#27ae60' }]}>
            <Text style={styles.actionButtonText}>Create Policy</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Policy Management Features:</Text>
        <View style={styles.featuresCard}>
          <Text style={styles.featureTitle}>Comprehensive Policy Control:</Text>
          <Text style={styles.feature}>• Version control with change tracking</Text>
          <Text style={styles.feature}>• Automated policy enforcement</Text>
          <Text style={styles.feature}>• Violation tracking and analytics</Text>
          <Text style={styles.feature}>• Multi-language policy support</Text>
          <Text style={styles.feature}>• Legal compliance monitoring</Text>
          <Text style={styles.feature}>• Policy effectiveness analysis</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Community rules → Community Rules Screen → Rule Detail Screen
          {'\n'}• Terms of use → Terms of Use Screen → Legal Document Editor
          {'\n'}• Privacy policy → Privacy Policy Screen → Data Protection Settings
          {'\n'}• Intellectual property → IP Policy Screen → Copyright Management
          {'\n'}• Policy updates → Update Preview Screen → Approval Workflow
        </Text>

        <Text style={styles.sectionTitle}>Compliance Requirements:</Text>
        <View style={styles.complianceCard}>
          <Text style={styles.complianceTitle}>Legal & Regulatory Compliance:</Text>
          <Text style={styles.compliance}>• GDPR compliance for EU users</Text>
          <Text style={styles.compliance}>• CCPA compliance for California residents</Text>
          <Text style={styles.compliance}>• COPPA compliance for users under 13</Text>
          <Text style={styles.compliance}>• Digital Services Act (DSA) compliance</Text>
          <Text style={styles.compliance}>• Local jurisdiction requirements</Text>
          <Text style={styles.compliance}>• Regular policy review and updates</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#1abc9c',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderTopWidth: 4,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  statLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'center',
    marginTop: 4,
  },
  policyCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  policyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  policyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  policyMeta: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginBottom: 4,
  },
  violationCount: {
    fontSize: 12,
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  policyDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    marginBottom: 8,
  },
  lastUpdated: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  updatesContainer: {
    marginBottom: 16,
  },
  updateCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  updateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  updatePolicy: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  updateDate: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  updateChange: {
    fontSize: 14,
    color: '#555',
    marginBottom: 4,
  },
  updateAuthor: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  featuresCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  complianceCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  complianceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  compliance: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
});
