import { Stack } from 'expo-router';
import React from 'react';
import { View, Text } from 'react-native';

const Screen = () => {
  const screenName = "Policy Implementation Screen";
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Stack.Screen options={{ title: screenName }} />
      <Text>{screenName}</Text>
    </View>
  );
};

export default Screen;
