import { Stack } from 'expo-router';

export default function PoliciesLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Policy Management',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="dashboard" 
        options={{ 
          title: 'Policy Dashboard',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="community-rules" 
        options={{ 
          title: 'Community Rules',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="terms-of-use" 
        options={{ 
          title: 'Terms of Use',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="privacy-policy" 
        options={{ 
          title: 'Privacy Policy',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="intellectual-property" 
        options={{ 
          title: 'Intellectual Property Policy',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="policy-update/[id]" 
        options={{ 
          title: 'Policy Update',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="implementation" 
        options={{ 
          title: 'Policy Implementation',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="violation-examples" 
        options={{ 
          title: 'Violation Examples',
          headerShown: false 
        }} 
      />
    </Stack>
  );
}
