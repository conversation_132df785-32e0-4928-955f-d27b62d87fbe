import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function CommunityRules() {
  const ruleCategories = [
    {
      id: 'content-standards',
      title: 'Content Standards',
      description: 'Guidelines for acceptable content on the platform',
      rules: [
        'No hate speech, harassment, or discrimination',
        'No explicit sexual content or nudity',
        'No violence, threats, or dangerous activities',
        'No spam, scams, or misleading information'
      ],
      violations: 156,
      severity: 'HIGH'
    },
    {
      id: 'user-behavior',
      title: 'User Behavior',
      description: 'Expected conduct and interaction guidelines',
      rules: [
        'Respect other users and their opinions',
        'No impersonation or identity theft',
        'No doxxing or sharing private information',
        'Report violations instead of retaliating'
      ],
      violations: 89,
      severity: 'MEDIUM'
    },
    {
      id: 'group-channel-rules',
      title: 'Group & Channel Rules',
      description: 'Specific guidelines for community spaces',
      rules: [
        'Group admins must moderate content actively',
        'Secret groups have enhanced monitoring',
        'No illegal activities or coordination',
        'Respect group-specific rules and themes'
      ],
      violations: 67,
      severity: 'HIGH'
    },
    {
      id: 'intellectual-property',
      title: 'Intellectual Property',
      description: 'Copyright and trademark protection',
      rules: [
        'No copyright infringement or piracy',
        'Respect trademark and brand rights',
        'Provide proper attribution when required',
        'Report IP violations promptly'
      ],
      violations: 34,
      severity: 'MEDIUM'
    }
  ];

  const enforcementActions = [
    { action: 'Warning', description: 'First-time minor violations', color: '#f39c12' },
    { action: 'Content Removal', description: 'Violating posts/media deleted', color: '#e67e22' },
    { action: 'Temporary Ban', description: '7-90 days suspension', color: '#e74c3c' },
    { action: 'Permanent Ban', description: 'Account permanently disabled', color: '#8e44ad' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Community Rules</Text>
        <Text style={styles.subtitle}>Platform Behavior Guidelines</Text>
        <Link href="/admin/policies/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Policies</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          The Community Rules screen provides comprehensive management of platform 
          behavior guidelines and content standards. It includes rule categories, 
          violation tracking, and enforcement procedures.
        </Text>

        <Text style={styles.sectionTitle}>Rule Categories:</Text>
        {ruleCategories.map((category) => (
          <View key={category.id} style={[
            styles.categoryCard,
            { borderLeftColor: category.severity === 'HIGH' ? '#e74c3c' : '#f39c12' }
          ]}>
            <View style={styles.categoryHeader}>
              <Text style={styles.categoryTitle}>{category.title}</Text>
              <View style={styles.categoryMeta}>
                <Text style={[
                  styles.severityBadge,
                  { backgroundColor: category.severity === 'HIGH' ? '#e74c3c' : '#f39c12' }
                ]}>
                  {category.severity}
                </Text>
                <Text style={styles.violationCount}>{category.violations} violations</Text>
              </View>
            </View>
            <Text style={styles.categoryDescription}>{category.description}</Text>
            <View style={styles.rulesList}>
              {category.rules.map((rule, index) => (
                <Text key={index} style={styles.rule}>• {rule}</Text>
              ))}
            </View>
            <TouchableOpacity style={styles.editButton}>
              <Text style={styles.editButtonText}>Edit Rules</Text>
            </TouchableOpacity>
          </View>
        ))}

        <Text style={styles.sectionTitle}>Enforcement Actions:</Text>
        <View style={styles.enforcementGrid}>
          {enforcementActions.map((action, index) => (
            <View key={index} style={[styles.actionCard, { borderTopColor: action.color }]}>
              <Text style={[styles.actionTitle, { color: action.color }]}>{action.action}</Text>
              <Text style={styles.actionDescription}>{action.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Rule Management Features:</Text>
        <View style={styles.featuresCard}>
          <Text style={styles.featureTitle}>Advanced Rule Management:</Text>
          <Text style={styles.feature}>• Version control with change history</Text>
          <Text style={styles.feature}>• Multi-language rule translations</Text>
          <Text style={styles.feature}>• Automated rule enforcement</Text>
          <Text style={styles.feature}>• Real-time violation tracking</Text>
          <Text style={styles.feature}>• Community feedback integration</Text>
          <Text style={styles.feature}>• Legal compliance verification</Text>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.quickActions}>
          <Link href="/admin/policies/violation-examples" asChild>
            <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#e74c3c' }]}>
              <Text style={styles.actionButtonText}>View Examples</Text>
            </TouchableOpacity>
          </Link>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#3498db' }]}>
            <Text style={styles.actionButtonText}>Edit Rules</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#27ae60' }]}>
            <Text style={styles.actionButtonText}>Add Category</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Rule selection → Rule Detail Screen → Edit Interface
          {'\n'}• Policy update → Policy Update Screen → Approval Workflow
          {'\n'}• Violation examples → Violation Examples Screen
          {'\n'}• Policy implementation → Policy Implementation Screen
        </Text>

        <Text style={styles.sectionTitle}>Differentiated Enforcement:</Text>
        <View style={styles.enforcementCard}>
          <Text style={styles.enforcementTitle}>Two-Tier Enforcement System:</Text>
          <Text style={styles.enforcement}>• Public/Private Groups: Automatic enforcement</Text>
          <Text style={styles.enforcement}>• Secret Groups: Manual review required</Text>
          <Text style={styles.enforcement}>• Escalation for terrorism/extremism</Text>
          <Text style={styles.enforcement}>• Appeal process for all actions</Text>
          <Text style={styles.enforcement}>• User notification for violations</Text>
          <Text style={styles.enforcement}>• Comprehensive audit logging</Text>
        </View>

        <Text style={styles.sectionTitle}>Rule Categories Overview:</Text>
        <View style={styles.overviewCard}>
          <Text style={styles.overviewTitle}>Platform-Wide Guidelines:</Text>
          <Text style={styles.overview}>• Racism and discrimination prohibited</Text>
          <Text style={styles.overview}>• Sexual content and nudity restrictions</Text>
          <Text style={styles.overview}>• Violence and threat prevention</Text>
          <Text style={styles.overview}>• Hate speech and harassment policies</Text>
          <Text style={styles.overview}>• Terrorism and extremism monitoring</Text>
          <Text style={styles.overview}>• Intellectual property protection</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e74c3c',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  categoryCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  categoryMeta: {
    alignItems: 'flex-end',
  },
  severityBadge: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginBottom: 4,
  },
  violationCount: {
    fontSize: 12,
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  categoryDescription: {
    fontSize: 14,
    color: '#555',
    marginBottom: 12,
    lineHeight: 20,
  },
  rulesList: {
    marginBottom: 12,
  },
  rule: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
  editButton: {
    backgroundColor: '#3498db',
    padding: 8,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  editButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  enforcementGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderTopWidth: 4,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 12,
    color: '#555',
    lineHeight: 16,
  },
  featuresCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  enforcementCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  enforcementTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  enforcement: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
  overviewCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  overviewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  overview: {
    fontSize: 14,
    color: '#555',
    marginBottom: 6,
    lineHeight: 20,
  },
});
